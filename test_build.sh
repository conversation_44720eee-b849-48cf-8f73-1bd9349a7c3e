#!/bin/bash

echo "🔧 Testing Flutter Build - TaskRabitInc"
echo "====================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}📋 $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Step 1: Check Flutter Installation
print_status "Checking Flutter installation..."
if command -v flutter &> /dev/null; then
    FLUTTER_VERSION=$(flutter --version | head -1)
    print_success "Flutter found: $FLUTTER_VERSION"
else
    print_error "Flutter not found in PATH"
    echo "Please install Flutter: https://docs.flutter.dev/get-started/install"
    exit 1
fi

# Step 2: Run Flutter Doctor
print_status "Running Flutter Doctor..."
flutter doctor --android-licenses > /dev/null 2>&1
flutter doctor > doctor_output.txt 2>&1
if grep -q "No issues found" doctor_output.txt; then
    print_success "Flutter Doctor - No issues found"
else
    print_warning "Flutter Doctor found some issues"
    echo "Check doctor_output.txt for details"
fi

# Step 3: Clean and Get Dependencies
print_status "Cleaning project and getting dependencies..."
flutter clean > /dev/null 2>&1
flutter pub get > /dev/null 2>&1
if [ $? -eq 0 ]; then
    print_success "Dependencies installed successfully"
else
    print_error "Failed to install dependencies"
    exit 1
fi

# Step 4: Analyze Code
print_status "Running code analysis..."
flutter analyze > analyze_output.txt 2>&1
if [ $? -eq 0 ]; then
    print_success "Code analysis passed"
else
    print_warning "Code analysis found issues"
    echo "Check analyze_output.txt for details"
fi

# Step 5: Test Build for Android
print_status "Testing Android build..."
if flutter build apk --debug > android_build.log 2>&1; then
    print_success "Android build successful"
    APK_PATH=$(find build -name "*.apk" | head -1)
    if [ -n "$APK_PATH" ]; then
        APK_SIZE=$(du -h "$APK_PATH" | cut -f1)
        print_success "APK created: $APK_SIZE"
    fi
else
    print_error "Android build failed"
    echo "Check android_build.log for details"
fi

# Step 6: Test iOS Build (if on macOS)
if [[ "$OSTYPE" == "darwin"* ]]; then
    print_status "Testing iOS build..."
    cd ios
    pod install > pod_install.log 2>&1
    cd ..
    
    if flutter build ios --debug --no-codesign > ios_build.log 2>&1; then
        print_success "iOS build successful"
    else
        print_error "iOS build failed"
        echo "Check ios_build.log and pod_install.log for details"
    fi
else
    print_warning "iOS build skipped (not on macOS)"
fi

# Step 7: Verify Key Files
print_status "Verifying key configuration files..."

# Check Firebase config
if [ -f "lib/firebase_options.dart" ]; then
    print_success "Firebase options file found"
else
    print_error "Firebase options file missing"
fi

# Check Android config
if [ -f "android/app/google-services.json" ]; then
    print_success "Android Google Services config found"
else
    print_error "Android Google Services config missing"
fi

# Check iOS config
if [ -f "ios/Runner/GoogleService-Info.plist" ]; then
    print_success "iOS Google Services config found"
else
    print_error "iOS Google Services config missing"
fi

# Step 8: Test Firebase Initialization (syntax check)
print_status "Checking Firebase configuration syntax..."
if grep -q "DefaultFirebaseOptions.currentPlatform" lib/config/firebase_config.dart; then
    print_success "Firebase configuration looks correct"
else
    print_error "Firebase configuration missing platform options"
fi

# Step 9: Summary
echo ""
echo -e "${BLUE}📊 Build Test Summary${NC}"
echo "===================="

if [ -f "build/app/outputs/flutter-apk/app-debug.apk" ] || [ -f "build/app/outputs/apk/debug/app-debug.apk" ]; then
    print_success "Android APK build: SUCCESS"
else
    print_error "Android APK build: FAILED"
fi

if [[ "$OSTYPE" == "darwin"* ]]; then
    if [ -d "build/ios/iphoneos/Runner.app" ]; then
        print_success "iOS app build: SUCCESS"
    else
        print_error "iOS app build: FAILED"
    fi
fi

echo ""
echo "📝 Log files created:"
echo "- doctor_output.txt (Flutter Doctor results)"
echo "- analyze_output.txt (Code analysis results)"
echo "- android_build.log (Android build log)"
if [[ "$OSTYPE" == "darwin"* ]]; then
    echo "- ios_build.log (iOS build log)"
    echo "- pod_install.log (iOS pod install log)"
fi

echo ""
print_success "Build test completed!"
echo "If there are any errors, check the log files for detailed information."