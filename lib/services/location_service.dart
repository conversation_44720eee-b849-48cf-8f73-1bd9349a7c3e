import 'dart:async';
import 'package:geolocator/geolocator.dart';
import 'package:geocoding/geocoding.dart';
import 'package:permission_handler/permission_handler.dart';
import '../models/location_data.dart';
import 'api_service.dart';

class LocationService {
  static final Map<String, StreamController<LocationUpdate>> _trackingStreams = {};
  static final Map<String, StreamSubscription<Position>> _locationSubscriptions = {};

  // Request location permissions
  static Future<bool> requestLocationPermission() async {
    final status = await Permission.location.request();
    return status.isGranted;
  }

  // Check if location services are enabled
  static Future<bool> isLocationServiceEnabled() async {
    return await Geolocator.isLocationServiceEnabled();
  }

  // Get current device location
  static Future<LocationData> getCurrentLocation() async {
    final hasPermission = await requestLocationPermission();
    if (!hasPermission) {
      throw Exception('Location permission not granted');
    }

    final isEnabled = await isLocationServiceEnabled();
    if (!isEnabled) {
      throw Exception('Location services are disabled');
    }

    try {
      final position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
      );

      return LocationData(
        latitude: position.latitude,
        longitude: position.longitude,
        address: await _getAddressFromCoordinates(position.latitude, position.longitude),
      );
    } catch (e) {
      throw Exception('Failed to get current location: $e');
    }
  }

  // Start tracking runner location for an errand
  static Stream<LocationUpdate> trackRunnerLocation(String errandId) {
    // Return existing stream if already tracking
    if (_trackingStreams.containsKey(errandId)) {
      return _trackingStreams[errandId]!.stream;
    }

    // Create new stream for this errand
    final controller = StreamController<LocationUpdate>.broadcast();
    _trackingStreams[errandId] = controller;

    // Start real location tracking
    _startLocationTracking(errandId, controller);

    return controller.stream;
  }

  // Get runner's current location for an errand
  static Future<LocationUpdate> getRunnerLocation(String errandId) async {
    try {
      // Try to get location from API first
      final response = await ApiService.get('/errands/$errandId/location');
      if (response['success'] == true && response['location'] != null) {
        return LocationUpdate.fromJson(response['location']);
      }
    } catch (e) {
      print('Failed to get runner location from API: $e');
    }

    // Fallback to device location if API fails
    try {
      final currentLocation = await getCurrentLocation();
      return LocationUpdate(
        errandId: errandId,
        location: currentLocation,
        timestamp: DateTime.now(),
        speed: 0.0,
        heading: 0.0,
        accuracy: 5.0,
        isMoving: false,
      );
    } catch (e) {
      throw Exception('Failed to get location: $e');
    }
  }

  // Update runner location to server
  static Future<void> updateRunnerLocation(String errandId, LocationData location) async {
    try {
      await ApiService.post('/errands/$errandId/location', {
        'latitude': location.latitude,
        'longitude': location.longitude,
        'address': location.address,
        'timestamp': DateTime.now().toIso8601String(),
      });
    } catch (e) {
      print('Failed to update runner location: $e');
    }
  }

  // Stop tracking location for an errand
  static void stopTracking(String errandId) {
    _locationSubscriptions[errandId]?.cancel();
    _locationSubscriptions.remove(errandId);
    
    _trackingStreams[errandId]?.close();
    _trackingStreams.remove(errandId);
  }

  // Private method to start real location tracking
  static void _startLocationTracking(String errandId, StreamController<LocationUpdate> controller) async {
    try {
      final hasPermission = await requestLocationPermission();
      if (!hasPermission) {
        controller.addError('Location permission not granted');
        return;
      }

      final locationStream = Geolocator.getPositionStream(
        locationSettings: const LocationSettings(
          accuracy: LocationAccuracy.high,
          distanceFilter: 10, // Update every 10 meters
          timeLimit: Duration(minutes: 1),
        ),
      );

      final subscription = locationStream.listen(
        (position) async {
          final address = await _getAddressFromCoordinates(
            position.latitude, 
            position.longitude,
          );

          final location = LocationData(
            latitude: position.latitude,
            longitude: position.longitude,
            address: address,
          );

          final update = LocationUpdate(
            errandId: errandId,
            location: location,
            timestamp: DateTime.now(),
            speed: position.speed,
            heading: position.heading,
            accuracy: position.accuracy,
            isMoving: position.speed > 0.5, // Consider moving if speed > 0.5 m/s
          );

          controller.add(update);

          // Update server with new location
          await updateRunnerLocation(errandId, location);
        },
        onError: (error) {
          controller.addError('Location tracking error: $error');
        },
      );

      _locationSubscriptions[errandId] = subscription;
    } catch (e) {
      controller.addError('Failed to start location tracking: $e');
    }
  }

  // Get address from coordinates (reverse geocoding)
  static Future<String> _getAddressFromCoordinates(double latitude, double longitude) async {
    try {
      final placemarks = await placemarkFromCoordinates(latitude, longitude);
      if (placemarks.isNotEmpty) {
        final place = placemarks.first;
        return '${place.street}, ${place.locality}, ${place.administrativeArea}';
      }
    } catch (e) {
      print('Reverse geocoding failed: $e');
    }
    return 'Unknown location';
  }

  // Calculate distance between two locations
  static double calculateDistance(LocationData from, LocationData to) {
    return Geolocator.distanceBetween(
      from.latitude,
      from.longitude,
      to.latitude,
      to.longitude,
    );
  }

  // Calculate route between two locations
  static Future<RouteInfo> calculateRoute(LocationData from, LocationData to) async {
    try {
      // For production, integrate with Google Maps Directions API or similar
      final distance = calculateDistance(from, to);
      final estimatedTime = (distance / 833.33).round(); // Assuming 30 km/h average speed
      
      return RouteInfo(
        distance: distance,
        estimatedTime: Duration(seconds: estimatedTime),
        polylinePoints: await _calculatePolyline(from, to),
      );
    } catch (e) {
      throw Exception('Failed to calculate route: $e');
    }
  }

  // Calculate polyline points for route (simplified)
  static Future<List<LocationData>> _calculatePolyline(LocationData from, LocationData to) async {
    // For production, use Google Maps Directions API to get actual route polyline
    // This is a simplified straight-line implementation
    final points = <LocationData>[];
    const steps = 10;
    
    for (int i = 0; i <= steps; i++) {
      final ratio = i / steps;
      final lat = from.latitude + (to.latitude - from.latitude) * ratio;
      final lng = from.longitude + (to.longitude - from.longitude) * ratio;
      
      points.add(LocationData(
        latitude: lat,
        longitude: lng,
        address: i == 0 ? from.address : (i == steps ? to.address : 'Route point'),
      ));
    }
    
    return points;
  }

  // Get cached location for an errand (offline support)
  static LocationUpdate? getCachedLocation(String errandId) {
    // Implementation would use local storage like SharedPreferences or Hive
    // For now, return null to indicate no cached data
    return null;
  }

  // Clear all tracking streams (cleanup)
  static void clearAllTracking() {
    for (final errandId in _trackingStreams.keys.toList()) {
      stopTracking(errandId);
    }
  }
}