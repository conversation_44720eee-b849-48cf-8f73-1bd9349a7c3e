import '../models/service.dart';
import '../models/notification.dart';
import '../config/api_config.dart';
import 'api_service.dart';
import 'service_api.dart';

class CustomerService {
  // Get all available services
  static Future<List<Service>> getServices() async {
    try {
      return await ServiceApi.getServices();
    } catch (e) {
      print('Error fetching services: $e');
      rethrow;
    }
  }

  // Get services by category
  static Future<List<Service>> getServicesByCategory(String category) async {
    try {
      return await ServiceApi.getServicesByCategory(category);
    } catch (e) {
      print('Error fetching services by category: $e');
      rethrow;
    }
  }

  // Get service details
  static Future<Service> getServiceById(String serviceId) async {
    try {
      return await ServiceApi.getServiceById(serviceId);
    } catch (e) {
      print('Error fetching service details: $e');
      rethrow;
    }
  }

  // Create a new booking
  static Future<Booking> createBooking(CreateBookingRequest request) async {
    try {
      return await ServiceApi.createBooking(request);
    } catch (e) {
      print('Error creating booking: $e');
      rethrow;
    }
  }

  // Get customer's bookings
  static Future<List<Booking>> getBookings() async {
    try {
      return await ServiceApi.getUserBookings();
    } catch (e) {
      print('Error fetching bookings: $e');
      rethrow;
    }
  }

  // Get booking details
  static Future<Booking> getBookingById(String bookingId) async {
    try {
      return await ServiceApi.getBookingById(bookingId);
    } catch (e) {
      print('Error fetching booking details: $e');
      rethrow;
    }
  }

  // Cancel a booking
  static Future<void> cancelBooking(String bookingId, String reason) async {
    try {
      await ServiceApi.cancelBooking(bookingId, reason);
    } catch (e) {
      print('Error cancelling booking: $e');
      rethrow;
    }
  }

  // Get active bookings
  static Future<List<Booking>> getActiveBookings() async {
    try {
      final allBookings = await ServiceApi.getUserBookings();
      return allBookings.where((booking) => 
        booking.status == BookingStatus.pending || 
        booking.status == BookingStatus.confirmed ||
        booking.status == BookingStatus.in_progress
      ).toList();
    } catch (e) {
      print('Error fetching active bookings: $e');
      rethrow;
    }
  }

  // Get completed bookings
  static Future<List<Booking>> getCompletedBookings() async {
    try {
      final allBookings = await ServiceApi.getUserBookings();
      return allBookings.where((booking) => 
        booking.status == BookingStatus.completed
      ).toList();
    } catch (e) {
      print('Error fetching completed bookings: $e');
      rethrow;
    }
  }

  // Get cancelled bookings
  static Future<List<Booking>> getCancelledBookings() async {
    try {
      final allBookings = await ServiceApi.getUserBookings();
      return allBookings.where((booking) => 
        booking.status == BookingStatus.cancelled
      ).toList();
    } catch (e) {
      print('Error fetching cancelled bookings: $e');
      rethrow;
    }
  }

  // Quick book a service
  static Future<Booking> quickBookService({
    required String serviceId,
    required DateTime scheduledDate,
    required String scheduledTime,
    required String serviceLocation,
    Map<String, dynamic>? locationCoordinates,
    String? specialInstructions,
  }) async {
    try {
      final request = CreateBookingRequest(
        serviceId: serviceId,
        bookingDate: scheduledDate,
        address: serviceLocation,
        locationCoordinates: locationCoordinates,
        specialInstructions: specialInstructions,
      );
      return await ServiceApi.createBooking(request);
    } catch (e) {
      print('Error quick booking service: $e');
      rethrow;
    }
  }

  // Get service categories
  static Future<List<String>> getServiceCategories() async {
    try {
      final services = await ServiceApi.getServices();
      final categories = services.map((service) => service.category).toSet().toList();
      categories.sort();
      return categories;
    } catch (e) {
      print('Error fetching service categories: $e');
      return ['Cleaning', 'Maintenance', 'Moving', 'Gardening', 'General'];
    }
  }

  // Search services
  static Future<List<Service>> searchServices(String query) async {
    try {
      final allServices = await ServiceApi.getServices();
      final searchLower = query.toLowerCase();
      return allServices.where((service) =>
        service.name.toLowerCase().contains(searchLower) ||
        service.description.toLowerCase().contains(searchLower) ||
        service.category.toLowerCase().contains(searchLower)
      ).toList();
    } catch (e) {
      print('Error searching services: $e');
      rethrow;
    }
  }

  // Get notifications
  static Future<List<AppNotification>> getNotifications() async {
    try {
      final response = await ApiService.get(ApiConfig.notificationsEndpoint);

      // Check if response contains a 'data' field with a list
      if (response['data'] is List) {
        final List<dynamic> notificationsList = response['data'] as List;
        return notificationsList
            .map((json) => AppNotification.fromJson(json as Map<String, dynamic>))
            .toList();
      }

      return [];
    } catch (e) {
      print('Error fetching notifications: $e');
      return [];
    }
  }

  // Mark notification as read
  static Future<void> markNotificationAsRead(String notificationId) async {
    try {
      final endpoint = ApiConfig.markNotificationReadEndpoint.replaceFirst('{id}', notificationId);
      await ApiService.patch(endpoint);
    } catch (e) {
      print('Error marking notification as read: $e');
      rethrow;
    }
  }

  // Register device token for push notifications
  static Future<void> registerDeviceToken(String token) async {
    try {
      await ApiService.post(
        ApiConfig.deviceTokenEndpoint,
        {'token': token},
      );
    } catch (e) {
      print('Error registering device token: $e');
      rethrow;
    }
  }

  // Update booking status (for tracking)
  static Future<void> updateBookingStatus(String bookingId, String status) async {
    try {
      await ServiceApi.updateBookingStatus(bookingId, status);
    } catch (e) {
      print('Error updating booking status: $e');
      rethrow;
    }
  }
}

class DeviceTokenRequest {
  final String token;

  DeviceTokenRequest({required this.token});

  Map<String, dynamic> toJson() {
    return {
      'token': token,
    };
  }
}