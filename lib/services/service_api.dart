import '../models/service.dart';
import '../config/api_config.dart';
import 'api_service.dart';

class ServiceApi {
  // Fetch all available services
  static Future<List<Service>> getServices() async {
    final response = await ApiService.get('/services');
    
    try {
      // Based on actual API testing: direct array in data field
      if (response['success'] == true && response['data'] is List) {
        final List<dynamic> servicesData = response['data'] as List;
        return servicesData.map((json) => Service.fromJson(json as Map<String, dynamic>)).toList();
      }
      return [];
    } catch (e) {
      print('Services API response structure mismatch: $e');
      print('Response structure: ${response.toString()}');
      return [];
    }
  }

  // Fetch service by ID
  static Future<Service> getServiceById(String serviceId) async {
    final response = await ApiService.get('${ApiConfig.servicesEndpoint}/$serviceId');
    
    if (response['success'] == true && response['data'] != null) {
      return Service.fromJson(response['data']);
    } else {
      throw Exception('Failed to load service details');
    }
  }

  // Search services by category
  static Future<List<Service>> getServicesByCategory(String category) async {
    final response = await ApiService.get(
      '${ApiConfig.servicesEndpoint}?category=$category',
    );
    
    try {
      if (response['success'] == true && response['data'] != null) {
        if (response['data'] is List) {
          final List<dynamic> servicesData = response['data'] as List;
          return servicesData.map((json) => Service.fromJson(json as Map<String, dynamic>)).toList();
        } else if (response['data'] is Map) {
          final dataMap = response['data'] as Map<String, dynamic>;
          if (dataMap['services'] is List) {
            final List<dynamic> servicesData = dataMap['services'] as List;
            return servicesData.map((json) => Service.fromJson(json as Map<String, dynamic>)).toList();
          }
        }
        return [];
      } else {
        throw Exception(response['message'] ?? 'Failed to load services by category');
      }
    } catch (e) {
      if (e.toString().contains('is not a subtype of type')) {
        print('Services by category API response structure mismatch: $e');
        return [];
      } else {
        rethrow;
      }
    }
  }

  // Create a new booking
  static Future<Booking> createBooking(CreateBookingRequest request) async {
    final response = await ApiService.post(
      ApiConfig.bookingsEndpoint,
      request.toJson(),
    );
    
    if (response['success'] == true && response['booking'] != null) {
      return Booking.fromJson(response['booking']);
    } else {
      throw Exception(response['message'] ?? 'Failed to create booking');
    }
  }

  // Get user's bookings
  static Future<List<Booking>> getUserBookings() async {
    final response = await ApiService.get('/bookings');
    
    try {
      // Based on actual API testing: paginated response with data.data structure
      if (response['success'] == true && response['data'] != null) {
        final dataMap = response['data'] as Map<String, dynamic>;
        if (dataMap['data'] is List) {
          final List<dynamic> bookingsData = dataMap['data'] as List;
          return bookingsData.map((json) => Booking.fromJson(json as Map<String, dynamic>)).toList();
        }
      }
      return [];
    } catch (e) {
      print('Bookings API response structure mismatch: $e');
      print('Response structure: ${response.toString()}');
      return [];
    }
  }

  // Get booking by ID
  static Future<Booking> getBookingById(String bookingId) async {
    final response = await ApiService.get('${ApiConfig.bookingsEndpoint}/$bookingId');
    
    if (response['success'] == true && response['data'] != null) {
      return Booking.fromJson(response['data']);
    } else {
      throw Exception('Failed to load booking details');
    }
  }

  // Cancel booking
  static Future<void> cancelBooking(String bookingId, String reason) async {
    final response = await ApiService.post(
      '${ApiConfig.bookingsEndpoint}/$bookingId/cancel',
      {'reason': reason},
    );
    
    if (response['success'] != true) {
      throw Exception(response['message'] ?? 'Failed to cancel booking');
    }
  }

  // Update booking status (for runners/agents)
  static Future<void> updateBookingStatus(String bookingId, String status) async {
    final response = await ApiService.put(
      '${ApiConfig.bookingsEndpoint}/$bookingId/status',
      {'status': status},
    );
    
    if (response['success'] != true) {
      throw Exception(response['message'] ?? 'Failed to update booking status');
    }
  }
}