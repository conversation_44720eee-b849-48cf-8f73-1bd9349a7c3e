import 'dart:async';
import 'package:geolocator/geolocator.dart';
import 'api_service.dart';
import 'errand_api.dart';

class TrackingService {
  static Timer? _locationTimer;
  static String? _activeErrandId;
  
  // Start tracking for an errand
  static Future<void> startTracking(String errandId) async {
    _activeErrandId = errandId;
    
    // Request location permissions
    final permission = await _checkLocationPermission();
    if (!permission) {
      throw TrackingException('Location permission denied');
    }
    
    // Update location every 30 seconds
    _locationTimer?.cancel();
    _locationTimer = Timer.periodic(const Duration(seconds: 30), (_) async {
      await _updateLocation();
    });
    
    // Send initial location
    await _updateLocation();
  }
  
  // Stop tracking
  static void stopTracking() {
    _locationTimer?.cancel();
    _locationTimer = null;
    _activeErrandId = null;
  }
  
  // Update current location
  static Future<void> _updateLocation() async {
    if (_activeErrandId == null) return;
    
    try {
      final position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
      );
      
      await ErrandApi.updateLocation(
        _activeErrandId!,
        position.latitude,
        position.longitude,
      );
      
      print('Location updated: ${position.latitude}, ${position.longitude}');
    } catch (e) {
      print('Failed to update location: $e');
    }
  }
  
  // Check and request location permission
  static Future<bool> _checkLocationPermission() async {
    bool serviceEnabled;
    LocationPermission permission;
    
    // Check if location services are enabled
    serviceEnabled = await Geolocator.isLocationServiceEnabled();
    if (!serviceEnabled) {
      throw TrackingException('Location services are disabled');
    }
    
    permission = await Geolocator.checkPermission();
    if (permission == LocationPermission.denied) {
      permission = await Geolocator.requestPermission();
      if (permission == LocationPermission.denied) {
        return false;
      }
    }
    
    if (permission == LocationPermission.deniedForever) {
      throw TrackingException('Location permissions are permanently denied');
    }
    
    return true;
  }
  
  // Get current location
  static Future<Position> getCurrentLocation() async {
    final permission = await _checkLocationPermission();
    if (!permission) {
      throw TrackingException('Location permission denied');
    }
    
    return await Geolocator.getCurrentPosition(
      desiredAccuracy: LocationAccuracy.high,
    );
  }
  
  // Calculate distance between two points
  static double calculateDistance(
    double startLatitude,
    double startLongitude,
    double endLatitude,
    double endLongitude,
  ) {
    return Geolocator.distanceBetween(
      startLatitude,
      startLongitude,
      endLatitude,
      endLongitude,
    );
  }
  
  // Get real-time tracking data for a client
  static Stream<Map<String, dynamic>> trackRunner(String errandId) {
    final controller = StreamController<Map<String, dynamic>>();
    
    Timer.periodic(const Duration(seconds: 10), (timer) async {
      try {
        final response = await ApiService.get('/errands/$errandId/tracking');
        
        if (response['success'] == true && response['data'] != null) {
          controller.add(response['data']);
        }
      } catch (e) {
        print('Error fetching tracking data: $e');
      }
    });
    
    return controller.stream;
  }
  
  // Estimate arrival time
  static Future<DateTime?> estimateArrivalTime(
    double currentLat,
    double currentLng,
    double destinationLat,
    double destinationLng,
  ) async {
    try {
      final distance = calculateDistance(
        currentLat,
        currentLng,
        destinationLat,
        destinationLng,
      );
      
      // Assume average speed of 30 km/h in city
      const averageSpeedKmh = 30.0;
      final timeInHours = (distance / 1000) / averageSpeedKmh;
      final timeInMinutes = (timeInHours * 60).round();
      
      return DateTime.now().add(Duration(minutes: timeInMinutes));
    } catch (e) {
      print('Error calculating arrival time: $e');
      return null;
    }
  }
}

class TrackingException implements Exception {
  final String message;
  
  TrackingException(this.message);
  
  @override
  String toString() => 'TrackingException: $message';
}