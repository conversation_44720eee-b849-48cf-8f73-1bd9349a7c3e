import 'dart:io';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/auth_response.dart';
import '../models/user.dart';
import '../config/api_config.dart';
import 'api_service.dart';

class AuthService {
  // Laravel Sanctum Mobile Authentication
  static Future<AuthResponse> login(LoginRequest request) async {
    final response = await ApiService.post(
      ApiConfig.loginEndpoint,
      request.toMobileJson(),
    );
    
    final authResponse = AuthResponse.fromJson(response);
    
    // Set auth token for Laravel Sanctum
    if (authResponse.success && authResponse.data != null) {
      final authData = authResponse.data!;
      ApiService.setAuthToken(authData.token);
      
      // Store token and user info locally
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('auth_token', authData.token);
      await prefs.setString('user_role', authData.user.role.toString().split('.').last);
      await prefs.setString('user_id', authData.user.id);
      await prefs.setString('user_name', authData.user.displayName);
      await prefs.setString('user_email', authData.user.email);
    }
    
    return authResponse;
  }
  
  static Future<AuthResponse> register(RegisterRequest request) async {
    final response = await ApiService.post(
      ApiConfig.registerEndpoint,
      request.toMobileJson(),
    );
    
    return AuthResponse.fromJson(response);
  }
  
  static Future<AuthResponse> forgotPassword(ForgotPasswordRequest request) async {
    final response = await ApiService.post(
      ApiConfig.forgotPasswordEndpoint,
      request.toJson(),
    );
    
    return AuthResponse.fromJson(response);
  }
  
  static Future<User> getUserProfile() async {
    final response = await ApiService.get('/user');
    
    try {
      // Based on actual API testing: /api/v1/user returns direct user object
      return User.fromJson(response);
    } catch (e) {
      print('User profile API response structure mismatch: $e');
      print('Response structure: ${response.toString()}');
      throw Exception('Failed to parse user profile data');
    }
  }
  
  static Future<void> updateProfile(UpdateProfileRequest request) async {
    await ApiService.put(
      ApiConfig.userProfileEndpoint,
      request.toJson(),
    );
  }
  
  static Future<String> uploadProfilePhoto(String filePath) async {
    final response = await ApiService.postMultipart(
      ApiConfig.userProfilePhotoEndpoint,
      File(filePath),
      'photo',
    );
    
    return response['path'];
  }
  
  // Laravel Sanctum logout
  static Future<void> logout() async {
    try {
      // Call logout endpoint to revoke Sanctum token
      await ApiService.post('/auth/logout', {});
    } catch (e) {
      // Continue with local logout even if API call fails
      print('Logout API call failed: $e');
    } finally {
      // Clear local storage and API token
      ApiService.clearAuthToken();
      final prefs = await SharedPreferences.getInstance();
      await prefs.clear();
    }
  }
  
  // Refresh token for mobile
  static Future<AuthResponse> refreshToken() async {
    try {
      final response = await ApiService.post(
        ApiConfig.refreshTokenEndpoint,
        {},
      );
      
      final authResponse = AuthResponse.fromJson(response);
      
      if (authResponse.success && authResponse.data != null) {
        final authData = authResponse.data!;
        ApiService.setAuthToken(authData.token);
        
        // Store new token
        final prefs = await SharedPreferences.getInstance();
        await prefs.setString('auth_token', authData.token);
      }
      
      return authResponse;
    } catch (e) {
      throw Exception('Failed to refresh token: $e');
    }
  }
  
  // Initialize auth from stored token
  static Future<bool> initializeAuth() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('auth_token');
      
      if (token != null) {
        ApiService.setAuthToken(token);
        
        // Verify token is still valid by making a profile request
        await getUserProfile();
        return true;
      }
      
      return false;
    } catch (e) {
      // Token is invalid, clear it
      final prefs = await SharedPreferences.getInstance();
      await prefs.clear();
      ApiService.clearAuthToken();
      return false;
    }
  }
  
  // Check if user is authenticated
  static Future<bool> isAuthenticated() async {
    final prefs = await SharedPreferences.getInstance();
    final token = prefs.getString('auth_token');
    return token != null;
  }
  
  // Get current user info from local storage
  static Future<Map<String, dynamic>?> getCurrentUserInfo() async {
    final prefs = await SharedPreferences.getInstance();
    final token = prefs.getString('auth_token');
    
    if (token == null) return null;
    
    return {
      'id': prefs.getString('user_id'),
      'name': prefs.getString('user_name'),
      'email': prefs.getString('user_email'),
      'role': prefs.getString('user_role'),
      'token': token,
    };
  }
}