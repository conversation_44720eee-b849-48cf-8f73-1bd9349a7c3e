import '../config/api_config.dart';
import 'api_service.dart';

class RatingService {
  // Submit a rating after errand completion
  static Future<void> submitRating({
    required String errandId,
    required String ratedUserId,
    required int rating,
    String? comment,
  }) async {
    try {
      final response = await ApiService.post(
        ApiConfig.ratingsEndpoint,
        {
          'errand_id': errandId,
          'rated_user_id': ratedUserId,
          'rating': rating,
          if (comment != null && comment.isNotEmpty) 'comment': comment,
        },
      );
      
      if (response['success'] != true) {
        throw RatingException(response['message'] ?? 'Failed to submit rating');
      }
    } catch (e) {
      if (e is RatingException) rethrow;
      throw RatingException('Failed to submit rating: ${e.toString()}');
    }
  }
  
  // Get ratings for a user
  static Future<List<Rating>> getUserRatings(String userId) async {
    try {
      final response = await ApiService.get('/users/$userId/ratings');
      
      if (response['success'] == true && response['data'] is List) {
        final List<dynamic> ratingsData = response['data'] as List;
        return ratingsData
            .map((json) => Rating.fromJson(json as Map<String, dynamic>))
            .toList();
      }
      return [];
    } catch (e) {
      print('Error fetching user ratings: $e');
      return [];
    }
  }
  
  // Get average rating for a user
  static Future<double> getUserAverageRating(String userId) async {
    try {
      final response = await ApiService.get('/users/$userId/rating-summary');
      
      if (response['success'] == true && response['data'] != null) {
        return double.tryParse(response['data']['average_rating'].toString()) ?? 0.0;
      }
      return 0.0;
    } catch (e) {
      print('Error fetching average rating: $e');
      return 0.0;
    }
  }
  
  // Check if rating exists for an errand
  static Future<bool> hasRatedErrand(String errandId) async {
    try {
      final response = await ApiService.get('/errands/$errandId/rating');
      
      if (response['success'] == true && response['data'] != null) {
        return response['data']['has_rated'] ?? false;
      }
      return false;
    } catch (e) {
      print('Error checking rating status: $e');
      return false;
    }
  }
  
  // Get rating for a specific errand
  static Future<Rating?> getErrandRating(String errandId) async {
    try {
      final response = await ApiService.get('/errands/$errandId/rating');
      
      if (response['success'] == true && response['data'] != null && response['data']['rating'] != null) {
        return Rating.fromJson(response['data']['rating']);
      }
      return null;
    } catch (e) {
      print('Error fetching errand rating: $e');
      return null;
    }
  }
}

class Rating {
  final String id;
  final String errandId;
  final String raterId;
  final String ratedUserId;
  final int rating;
  final String? comment;
  final DateTime createdAt;
  final String? raterName;
  final String? raterProfilePicture;
  
  Rating({
    required this.id,
    required this.errandId,
    required this.raterId,
    required this.ratedUserId,
    required this.rating,
    this.comment,
    required this.createdAt,
    this.raterName,
    this.raterProfilePicture,
  });
  
  factory Rating.fromJson(Map<String, dynamic> json) {
    return Rating(
      id: json['id'],
      errandId: json['errand_id'],
      raterId: json['rater_id'],
      ratedUserId: json['rated_user_id'],
      rating: json['rating'],
      comment: json['comment'],
      createdAt: DateTime.parse(json['created_at']),
      raterName: json['rater']?['name'],
      raterProfilePicture: json['rater']?['profile_picture'],
    );
  }
  
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'errand_id': errandId,
      'rater_id': raterId,
      'rated_user_id': ratedUserId,
      'rating': rating,
      'comment': comment,
      'created_at': createdAt.toIso8601String(),
      'rater_name': raterName,
      'rater_profile_picture': raterProfilePicture,
    };
  }
}

class RatingException implements Exception {
  final String message;
  
  RatingException(this.message);
  
  @override
  String toString() => 'RatingException: $message';
}