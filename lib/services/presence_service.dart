import 'dart:async';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:geolocator/geolocator.dart';
import '../config/firebase_config.dart';
import 'api_service.dart';
import 'auth_service.dart';

class PresenceService {
  static final FirebaseFirestore _firestore = FirebaseConfig.firestore;
  static Timer? _presenceTimer;
  static Timer? _locationTimer;
  static String? _currentUserId;
  static String? _currentUserRole;
  static StreamSubscription<DocumentSnapshot>? _presenceSubscription;
  
  // Collection names
  static const String _runnersCollection = 'runners_presence';
  static const String _clientsCollection = 'clients_presence';
  
  // Initialize presence system
  static Future<void> initialize() async {
    try {
      final userInfo = await AuthService.getCurrentUserInfo();
      if (userInfo != null) {
        _currentUserId = userInfo['id'];
        _currentUserRole = userInfo['role'];
        
        await _startPresenceTracking();
        print('Presence service initialized for user: $_currentUserId');
      }
    } catch (e) {
      print('Error initializing presence service: $e');
    }
  }
  
  // Start presence tracking
  static Future<void> _startPresenceTracking() async {
    if (_currentUserId == null || _currentUserRole == null) return;
    
    // Set initial online status
    await setOnlineStatus(true);
    
    // Update presence every 30 seconds
    _presenceTimer?.cancel();
    _presenceTimer = Timer.periodic(const Duration(seconds: 30), (_) async {
      await _updatePresence();
    });
    
    // For runners, also track location
    if (_currentUserRole == 'agent') {
      await _startLocationTracking();
    }
  }
  
  // Start location tracking for runners
  static Future<void> _startLocationTracking() async {
    if (_currentUserRole != 'agent') return;
    
    try {
      // Check location permissions
      final permission = await _checkLocationPermission();
      if (!permission) return;
      
      // Update location every 45 seconds
      _locationTimer?.cancel();
      _locationTimer = Timer.periodic(const Duration(seconds: 45), (_) async {
        await _updateRunnerLocation();
      });
      
      // Send initial location
      await _updateRunnerLocation();
    } catch (e) {
      print('Error starting location tracking: $e');
    }
  }
  
  // Update runner location
  static Future<void> _updateRunnerLocation() async {
    if (_currentUserId == null || _currentUserRole != 'agent') return;
    
    try {
      final position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
      );
      
      await _firestore
          .collection(_runnersCollection)
          .doc(_currentUserId)
          .update({
        'location': {
          'latitude': position.latitude,
          'longitude': position.longitude,
          'accuracy': position.accuracy,
          'timestamp': FieldValue.serverTimestamp(),
        },
        'last_seen': FieldValue.serverTimestamp(),
      });
      
      print('Location updated: ${position.latitude}, ${position.longitude}');
    } catch (e) {
      print('Error updating location: $e');
    }
  }
  
  // Check location permission
  static Future<bool> _checkLocationPermission() async {
    bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
    if (!serviceEnabled) return false;
    
    LocationPermission permission = await Geolocator.checkPermission();
    if (permission == LocationPermission.denied) {
      permission = await Geolocator.requestPermission();
      if (permission == LocationPermission.denied) return false;
    }
    
    if (permission == LocationPermission.deniedForever) return false;
    
    return true;
  }
  
  // Update presence information
  static Future<void> _updatePresence() async {
    if (_currentUserId == null || _currentUserRole == null) return;
    
    try {
      final collection = _currentUserRole == 'agent' 
          ? _runnersCollection 
          : _clientsCollection;
      
      final docRef = _firestore.collection(collection).doc(_currentUserId);
      
      await docRef.update({
        'last_seen': FieldValue.serverTimestamp(),
        'is_online': true,
      });
    } catch (e) {
      // Document might not exist, create it
      await _createPresenceDocument();
    }
  }
  
  // Create presence document
  static Future<void> _createPresenceDocument() async {
    if (_currentUserId == null || _currentUserRole == null) return;
    
    try {
      final userInfo = await AuthService.getCurrentUserInfo();
      if (userInfo == null) return;
      
      final collection = _currentUserRole == 'agent' 
          ? _runnersCollection 
          : _clientsCollection;
      
      final docRef = _firestore.collection(collection).doc(_currentUserId);
      
      final data = {
        'user_id': _currentUserId,
        'name': userInfo['name'] ?? 'Unknown',
        'email': userInfo['email'] ?? '',
        'role': _currentUserRole,
        'is_online': true,
        'is_available': _currentUserRole == 'agent' ? true : null,
        'last_seen': FieldValue.serverTimestamp(),
        'created_at': FieldValue.serverTimestamp(),
      };
      
      if (_currentUserRole == 'agent') {
        data['location'] = null;
        data['current_errand_id'] = null;
        data['status'] = 'available'; // available, busy, offline
      }
      
      await docRef.set(data, SetOptions(merge: true));
      print('Presence document created for user: $_currentUserId');
    } catch (e) {
      print('Error creating presence document: $e');
    }
  }
  
  // Set online/offline status
  static Future<void> setOnlineStatus(bool isOnline) async {
    if (_currentUserId == null || _currentUserRole == null) return;
    
    try {
      final collection = _currentUserRole == 'agent' 
          ? _runnersCollection 
          : _clientsCollection;
      
      final docRef = _firestore.collection(collection).doc(_currentUserId);
      
      if (isOnline) {
        await _createPresenceDocument();
        if (!isOnline) {
          await _startPresenceTracking();
        }
      } else {
        await docRef.update({
          'is_online': false,
          'last_seen': FieldValue.serverTimestamp(),
        });
        
        // Stop timers when going offline
        _presenceTimer?.cancel();
        _locationTimer?.cancel();
      }
      
      print('Online status set to: $isOnline');
    } catch (e) {
      print('Error setting online status: $e');
    }
  }
  
  // Set runner availability (for agents only)
  static Future<void> setRunnerAvailability(bool isAvailable) async {
    if (_currentUserId == null || _currentUserRole != 'agent') return;
    
    try {
      final docRef = _firestore.collection(_runnersCollection).doc(_currentUserId);
      
      await docRef.update({
        'is_available': isAvailable,
        'status': isAvailable ? 'available' : 'unavailable',
        'last_seen': FieldValue.serverTimestamp(),
      });
      
      // Also update backend API
      await ApiService.post('/runners/availability', {
        'is_available': isAvailable,
      });
      
      print('Runner availability set to: $isAvailable');
    } catch (e) {
      print('Error setting runner availability: $e');
    }
  }
  
  // Set runner status (available, busy, offline)
  static Future<void> setRunnerStatus(RunnerStatus status, {String? errandId}) async {
    if (_currentUserId == null || _currentUserRole != 'agent') return;
    
    try {
      final docRef = _firestore.collection(_runnersCollection).doc(_currentUserId);
      
      final data = {
        'status': status.name,
        'is_available': status == RunnerStatus.available,
        'last_seen': FieldValue.serverTimestamp(),
      };
      
      if (status == RunnerStatus.busy && errandId != null) {
        data['current_errand_id'] = errandId;
      } else {
        data['current_errand_id'] = null;
      }
      
      await docRef.update(data);
      
      print('Runner status set to: ${status.name}');
    } catch (e) {
      print('Error setting runner status: $e');
    }
  }
  
  // Get online runners near location
  static Stream<List<RunnerPresence>> getOnlineRunnersNearLocation(
    double latitude,
    double longitude,
    double radiusKm,
  ) {
    return _firestore
        .collection(_runnersCollection)
        .where('is_online', isEqualTo: true)
        .where('is_available', isEqualTo: true)
        .snapshots()
        .map((snapshot) {
      final runners = snapshot.docs
          .map((doc) => RunnerPresence.fromFirestore(doc))
          .where((runner) {
        if (runner.location == null) return false;
        
        final distance = Geolocator.distanceBetween(
          latitude,
          longitude,
          runner.location!.latitude,
          runner.location!.longitude,
        );
        
        return distance <= (radiusKm * 1000); // Convert km to meters
      }).toList();
      
      // Sort by distance
      runners.sort((a, b) {
        final distanceA = Geolocator.distanceBetween(
          latitude, longitude,
          a.location!.latitude, a.location!.longitude,
        );
        final distanceB = Geolocator.distanceBetween(
          latitude, longitude,
          b.location!.latitude, b.location!.longitude,
        );
        return distanceA.compareTo(distanceB);
      });
      
      return runners;
    });
  }
  
  // Get runner presence by ID
  static Stream<RunnerPresence?> getRunnerPresence(String runnerId) {
    return _firestore
        .collection(_runnersCollection)
        .doc(runnerId)
        .snapshots()
        .map((doc) {
      if (doc.exists) {
        return RunnerPresence.fromFirestore(doc);
      }
      return null;
    });
  }
  
  // Track runner for specific errand
  static Stream<RunnerPresence?> trackRunnerForErrand(String runnerId) {
    return getRunnerPresence(runnerId);
  }
  
  // Get all online runners
  static Stream<List<RunnerPresence>> getOnlineRunners() {
    return _firestore
        .collection(_runnersCollection)
        .where('is_online', isEqualTo: true)
        .snapshots()
        .map((snapshot) => snapshot.docs
            .map((doc) => RunnerPresence.fromFirestore(doc))
            .toList());
  }
  
  // Clean up when user logs out
  static Future<void> cleanup() async {
    await setOnlineStatus(false);
    
    _presenceTimer?.cancel();
    _locationTimer?.cancel();
    _presenceSubscription?.cancel();
    
    _presenceTimer = null;
    _locationTimer = null;
    _presenceSubscription = null;
    _currentUserId = null;
    _currentUserRole = null;
    
    print('Presence service cleaned up');
  }
  
  // Send FCM token to backend
  static Future<void> updateFCMToken(String token) async {
    try {
      await ApiService.post('/notifications/device', {
        'token': token,
        'platform': 'android', // or 'ios'
      });
      
      // Also store in Firestore for real-time targeting
      if (_currentUserId != null && _currentUserRole != null) {
        final collection = _currentUserRole == 'agent' 
            ? _runnersCollection 
            : _clientsCollection;
        
        await _firestore
            .collection(collection)
            .doc(_currentUserId)
            .update({'fcm_token': token});
      }
      
      print('FCM token updated successfully');
    } catch (e) {
      print('Error updating FCM token: $e');
    }
  }
}

enum RunnerStatus {
  available,
  busy,
  unavailable,
  offline,
}

class RunnerPresence {
  final String userId;
  final String name;
  final String email;
  final bool isOnline;
  final bool isAvailable;
  final RunnerStatus status;
  final RunnerLocation? location;
  final String? currentErrandId;
  final DateTime? lastSeen;
  final String? fcmToken;
  
  RunnerPresence({
    required this.userId,
    required this.name,
    required this.email,
    required this.isOnline,
    required this.isAvailable,
    required this.status,
    this.location,
    this.currentErrandId,
    this.lastSeen,
    this.fcmToken,
  });
  
  factory RunnerPresence.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    
    return RunnerPresence(
      userId: doc.id,
      name: data['name'] ?? '',
      email: data['email'] ?? '',
      isOnline: data['is_online'] ?? false,
      isAvailable: data['is_available'] ?? false,
      status: RunnerStatus.values.firstWhere(
        (s) => s.name == data['status'],
        orElse: () => RunnerStatus.offline,
      ),
      location: data['location'] != null 
          ? RunnerLocation.fromMap(data['location'])
          : null,
      currentErrandId: data['current_errand_id'],
      lastSeen: data['last_seen'] != null 
          ? (data['last_seen'] as Timestamp).toDate()
          : null,
      fcmToken: data['fcm_token'],
    );
  }
  
  Map<String, dynamic> toMap() {
    return {
      'user_id': userId,
      'name': name,
      'email': email,
      'is_online': isOnline,
      'is_available': isAvailable,
      'status': status.name,
      'location': location?.toMap(),
      'current_errand_id': currentErrandId,
      'last_seen': lastSeen != null ? Timestamp.fromDate(lastSeen!) : null,
      'fcm_token': fcmToken,
    };
  }
}

class RunnerLocation {
  final double latitude;
  final double longitude;
  final double accuracy;
  final DateTime timestamp;
  
  RunnerLocation({
    required this.latitude,
    required this.longitude,
    required this.accuracy,
    required this.timestamp,
  });
  
  factory RunnerLocation.fromMap(Map<String, dynamic> map) {
    return RunnerLocation(
      latitude: map['latitude']?.toDouble() ?? 0.0,
      longitude: map['longitude']?.toDouble() ?? 0.0,
      accuracy: map['accuracy']?.toDouble() ?? 0.0,
      timestamp: map['timestamp'] != null 
          ? (map['timestamp'] as Timestamp).toDate()
          : DateTime.now(),
    );
  }
  
  Map<String, dynamic> toMap() {
    return {
      'latitude': latitude,
      'longitude': longitude,
      'accuracy': accuracy,
      'timestamp': Timestamp.fromDate(timestamp),
    };
  }
}