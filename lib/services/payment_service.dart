import 'api_service.dart';

class PaymentService {
  static Future<PaymentResponse> processPayment({
    required String bookingId,
    required PaymentMethod paymentMethod,
    required double amount,
    String? phoneNumber,
    Map<String, dynamic>? cardDetails,
  }) async {
    try {
      final requestBody = {
        'booking_id': bookingId,
        'payment_method': paymentMethod.toString().split('.').last,
        'amount': amount,
      };

      if (paymentMethod == PaymentMethod.ecocash) {
        if (phoneNumber == null) {
          throw PaymentException('Phone number is required for EcoCash payments');
        }
        requestBody['phone_number'] = phoneNumber;
      } else if (paymentMethod == PaymentMethod.zipit) {
        if (phoneNumber == null) {
          throw PaymentException('Phone number is required for Zipit payments');
        }
        requestBody['phone_number'] = phoneNumber;
      }

      if (paymentMethod == PaymentMethod.card && cardDetails != null) {
        requestBody['card_details'] = cardDetails;
      }

      String endpoint;
      if (paymentMethod == PaymentMethod.ecocash) {
        endpoint = '/payments/ecocash';
      } else {
        endpoint = '/payments/process';
      }
      
      final response = await ApiService.post(endpoint, requestBody);
      
      if (response['success'] == true && response['data'] != null) {
        return PaymentResponse.fromJson(response['data']);
      } else {
        throw PaymentException(response['message'] ?? 'Payment failed');
      }
    } catch (e) {
      if (e is PaymentException) rethrow;
      throw PaymentException('Failed to process payment: ${e.toString()}');
    }
  }

  static Future<PaymentStatus> getPaymentStatus(String transactionId) async {
    try {
      final response = await ApiService.get('/payments/$transactionId/status');
      
      if (response['success'] == true && response['data'] != null) {
        final status = response['data']['status'] as String;
        return PaymentStatus.values.firstWhere(
          (s) => s.toString().split('.').last == status,
          orElse: () => PaymentStatus.pending,
        );
      } else {
        throw PaymentException('Failed to get payment status');
      }
    } catch (e) {
      throw PaymentException('Failed to check payment status: ${e.toString()}');
    }
  }

  static Future<List<PaymentHistory>> getPaymentHistory() async {
    try {
      final response = await ApiService.get('/payments/history');
      
      if (response['success'] == true && response['data'] is List) {
        final List<dynamic> paymentsData = response['data'] as List;
        return paymentsData
            .map((json) => PaymentHistory.fromJson(json as Map<String, dynamic>))
            .toList();
      }
      return [];
    } catch (e) {
      print('Error fetching payment history: $e');
      return [];
    }
  }

  static Future<PaymentMethods> getAvailablePaymentMethods() async {
    try {
      // Use Zimbabwe-specific payment methods endpoint
      final response = await ApiService.get('/payments/methods/zimbabwe');
      
      if (response['success'] == true && response['data'] != null) {
        return PaymentMethods.fromJson(response['data']);
      }
      
      // Return default payment methods if API fails
      return PaymentMethods(
        methods: [
          PaymentMethodInfo(
            method: PaymentMethod.ecocash,
            enabled: true,
            configured: true,
            name: 'EcoCash',
            icon: 'ecocash',
          ),
          PaymentMethodInfo(
            method: PaymentMethod.zipit,
            enabled: true,
            configured: true,
            name: 'Zipit',
            icon: 'zipit',
          ),
          PaymentMethodInfo(
            method: PaymentMethod.card,
            enabled: true,
            configured: true,
            name: 'Credit/Debit Card',
            icon: 'card',
          ),
        ],
      );
    } catch (e) {
      print('Error fetching payment methods: $e');
      throw PaymentException('Failed to get payment methods');
    }
  }
}

enum PaymentMethod {
  ecocash,
  zipit,
  card,
  bank_transfer,
}

enum PaymentStatus {
  pending,
  processing,
  completed,
  failed,
  cancelled,
  refunded,
}

class PaymentResponse {
  final String transactionId;
  final String bookingId;
  final double amount;
  final String currency;
  final PaymentStatus status;
  final String? reference;
  final DateTime createdAt;

  PaymentResponse({
    required this.transactionId,
    required this.bookingId,
    required this.amount,
    required this.currency,
    required this.status,
    this.reference,
    required this.createdAt,
  });

  factory PaymentResponse.fromJson(Map<String, dynamic> json) {
    return PaymentResponse(
      transactionId: json['transaction_id'],
      bookingId: json['booking_id'],
      amount: double.tryParse(json['amount'].toString()) ?? 0.0,
      currency: json['currency'] ?? 'USD',
      status: PaymentStatus.values.firstWhere(
        (s) => s.toString().split('.').last == json['status'],
        orElse: () => PaymentStatus.pending,
      ),
      reference: json['reference'],
      createdAt: DateTime.parse(json['created_at']),
    );
  }
}

class PaymentHistory {
  final String id;
  final String bookingId;
  final double amount;
  final String currency;
  final PaymentMethod method;
  final PaymentStatus status;
  final String? reference;
  final DateTime createdAt;

  PaymentHistory({
    required this.id,
    required this.bookingId,
    required this.amount,
    required this.currency,
    required this.method,
    required this.status,
    this.reference,
    required this.createdAt,
  });

  factory PaymentHistory.fromJson(Map<String, dynamic> json) {
    return PaymentHistory(
      id: json['id'],
      bookingId: json['booking_id'],
      amount: double.tryParse(json['amount'].toString()) ?? 0.0,
      currency: json['currency'] ?? 'USD',
      method: PaymentMethod.values.firstWhere(
        (m) => m.toString().split('.').last == json['payment_method'],
        orElse: () => PaymentMethod.ecocash,
      ),
      status: PaymentStatus.values.firstWhere(
        (s) => s.toString().split('.').last == json['status'],
        orElse: () => PaymentStatus.pending,
      ),
      reference: json['reference'],
      createdAt: DateTime.parse(json['created_at']),
    );
  }
}

class PaymentMethods {
  final List<PaymentMethodInfo> methods;

  PaymentMethods({required this.methods});

  factory PaymentMethods.fromJson(Map<String, dynamic> json) {
    final List<PaymentMethodInfo> methods = [];
    
    if (json['ecocash'] != null) {
      methods.add(PaymentMethodInfo(
        method: PaymentMethod.ecocash,
        enabled: json['ecocash']['enabled'] ?? false,
        configured: json['ecocash']['configured'] ?? false,
        name: 'EcoCash',
        icon: 'ecocash',
      ));
    }
    
    if (json['zipit'] != null) {
      methods.add(PaymentMethodInfo(
        method: PaymentMethod.zipit,
        enabled: json['zipit']['enabled'] ?? false,
        configured: json['zipit']['configured'] ?? false,
        name: 'Zipit',
        icon: 'zipit',
      ));
    }
    
    if (json['card'] != null) {
      methods.add(PaymentMethodInfo(
        method: PaymentMethod.card,
        enabled: json['card']['enabled'] ?? false,
        configured: json['card']['configured'] ?? false,
        name: 'Credit/Debit Card',
        icon: 'card',
      ));
    }
    
    return PaymentMethods(methods: methods);
  }
}

class PaymentMethodInfo {
  final PaymentMethod method;
  final bool enabled;
  final bool configured;
  final String name;
  final String icon;

  PaymentMethodInfo({
    required this.method,
    required this.enabled,
    required this.configured,
    required this.name,
    required this.icon,
  });
}

class PaymentException implements Exception {
  final String message;

  PaymentException(this.message);

  @override
  String toString() => 'PaymentException: $message';
}