import '../models/errand.dart';
import '../config/api_config.dart';
import 'api_service.dart';

class ErrandApi {
  // Create a new errand (Client)
  static Future<Errand> createErrand(CreateErrandRequest request) async {
    final response = await ApiService.post(
      ApiConfig.errandsEndpoint,
      request.toJson(),
    );
    
    if (response['success'] == true && response['data'] != null) {
      return Errand.fromJson(response['data']);
    } else {
      throw Exception(response['message'] ?? 'Failed to create errand');
    }
  }
  
  // Get user's errands (Client)
  static Future<List<Errand>> getUserErrands() async {
    final response = await ApiService.get(ApiConfig.userErrandsEndpoint);
    
    if (response['success'] == true && response['data'] != null) {
      if (response['data'] is List) {
        final List<dynamic> errandsData = response['data'] as List;
        return errandsData.map((json) => Errand.fromJson(json)).toList();
      } else if (response['data']['data'] is List) {
        // Paginated response
        final List<dynamic> errandsData = response['data']['data'] as List;
        return errandsData.map((json) => Errand.fromJson(json)).toList();
      }
    }
    return [];
  }
  
  // Update pending errand (Client)
  static Future<void> updateErrand(String errandId, Map<String, dynamic> updates) async {
    final response = await ApiService.put(
      '${ApiConfig.errandsEndpoint}/$errandId',
      updates,
    );
    
    if (response['success'] != true) {
      throw Exception(response['message'] ?? 'Failed to update errand');
    }
  }
  
  // Cancel errand (Client)
  static Future<void> cancelErrand(String errandId, String reason) async {
    final response = await ApiService.post(
      '/bookings/$errandId/cancel',
      {'reason': reason},
    );
    
    if (response['success'] != true) {
      throw Exception(response['message'] ?? 'Failed to cancel errand');
    }
  }
  
  // Get available errands for runners
  static Future<ErrandListResponse> getAvailableErrands({int page = 1}) async {
    final response = await ApiService.get(
      '${ApiConfig.availableErrandsEndpoint}?page=$page',
    );
    
    if (response['success'] == true) {
      return ErrandListResponse.fromJson(response);
    } else {
      throw Exception(response['message'] ?? 'Failed to load available errands');
    }
  }

  // Get runner's active errands
  static Future<List<Errand>> getMyErrands() async {
    final response = await ApiService.get(ApiConfig.runnerErrandsEndpoint);
    
    if (response['success'] == true && response['data'] != null) {
      final List<dynamic> errandsData = response['data'];
      return errandsData.map((json) => Errand.fromJson(json)).toList();
    } else {
      throw Exception('Failed to load your errands');
    }
  }

  // Accept an errand
  static Future<void> acceptErrand(String errandId) async {
    final response = await ApiService.post(
      ApiConfig.acceptErrandEndpoint.replaceAll('{id}', errandId),
      {},
    );
    
    if (response['success'] != true) {
      throw Exception(response['message'] ?? 'Failed to accept errand');
    }
  }

  // Update errand status
  static Future<void> updateErrandStatus(String errandId, String status) async {
    final response = await ApiService.put(
      '/errands/$errandId/status',
      {'status': status},
    );
    
    if (response['success'] != true) {
      throw Exception(response['message'] ?? 'Failed to update errand status');
    }
  }

  // Start an errand
  static Future<void> startErrand(String errandId) async {
    final endpoint = ApiConfig.startErrandEndpoint.replaceAll('{id}', errandId);
    final response = await ApiService.post(endpoint, {});
    
    if (response['success'] != true) {
      throw Exception(response['message'] ?? 'Failed to start errand');
    }
  }
  
  // Complete an errand
  static Future<void> completeErrand(String errandId, {String? notes}) async {
    final endpoint = ApiConfig.completeErrandEndpoint.replaceAll('{id}', errandId);
    final response = await ApiService.post(
      endpoint,
      notes != null ? {'notes': notes} : {},
    );
    
    if (response['success'] != true) {
      throw Exception(response['message'] ?? 'Failed to complete errand');
    }
  }

  // Update runner location for active errand
  static Future<void> updateLocation(String errandId, double latitude, double longitude) async {
    final endpoint = ApiConfig.updateLocationEndpoint.replaceAll('{id}', errandId);
    final response = await ApiService.post(endpoint, {
      'latitude': latitude,
      'longitude': longitude,
      'timestamp': DateTime.now().toIso8601String(),
    });
    
    if (response['success'] != true) {
      throw Exception(response['message'] ?? 'Failed to update location');
    }
  }
  
  // Get errand details
  static Future<Errand> getErrandById(String errandId) async {
    final response = await ApiService.get('${ApiConfig.errandsEndpoint}/$errandId');
    
    if (response['success'] == true && response['data'] != null) {
      return Errand.fromJson(response['data']);
    } else {
      throw Exception('Failed to load errand details');
    }
  }

  // Get runner earnings history
  static Future<List<RunnerEarning>> getEarningsHistory() async {
    final response = await ApiService.get('/runners/earnings/history');

    try {
      // Based on actual API testing: { success: true, data: { summary: {...}, transactions: [...] } }
      if (response['success'] == true && response['data'] != null) {
        final dataMap = response['data'] as Map<String, dynamic>;
        if (dataMap['transactions'] is List) {
          final List<dynamic> transactionsData = dataMap['transactions'] as List;
          return transactionsData.map((json) => RunnerEarning.fromJson(json as Map<String, dynamic>)).toList();
        }
      }
      return [];
    } catch (e) {
      print('Earnings API response structure mismatch: $e');
      print('Response structure: ${response.toString()}');
      return [];
    }
  }

  // Get runner profile
  static Future<Map<String, dynamic>> getRunnerProfile() async {
    final response = await ApiService.get(ApiConfig.runnerProfileEndpoint);
    
    if (response['success'] == true && response['data'] != null) {
      return response['data'];
    } else {
      throw Exception('Failed to load runner profile');
    }
  }
  
  // Update runner profile
  static Future<void> updateRunnerProfile(Map<String, dynamic> profileData) async {
    final response = await ApiService.put(ApiConfig.runnerProfileEndpoint, profileData);
    
    if (response['success'] != true) {
      throw Exception(response['message'] ?? 'Failed to update profile');
    }
  }
  
  // Get verification status
  static Future<Map<String, dynamic>> getVerificationStatus() async {
    final response = await ApiService.get(ApiConfig.runnerVerificationStatusEndpoint);
    
    if (response['success'] == true && response['data'] != null) {
      return response['data'];
    } else {
      throw Exception('Failed to load verification status');
    }
  }
  
  // Get performance overview
  static Future<Map<String, dynamic>> getPerformanceOverview() async {
    final response = await ApiService.get(ApiConfig.runnerPerformanceEndpoint);
    
    if (response['success'] == true && response['data'] != null) {
      return response['data'];
    } else {
      throw Exception('Failed to load performance overview');
    }
  }
  
  // Request withdrawal (legacy endpoint - may need update)
  static Future<void> requestWithdrawal(WithdrawRequest request) async {
    final response = await ApiService.post(
      '/runners/earnings/withdraw',
      request.toJson(),
    );
    
    if (response['success'] != true) {
      throw Exception(response['message'] ?? 'Failed to request withdrawal');
    }
  }

  // Update runner availability (legacy endpoint - may need update)
  static Future<void> updateAvailability(List<Map<String, dynamic>> availability) async {
    final response = await ApiService.put(
      '/runners/availability',
      {'availability': availability},
    );
    
    if (response['success'] != true) {
      throw Exception(response['message'] ?? 'Failed to update availability');
    }
  }

  // Search errands by filters
  static Future<ErrandListResponse> searchErrands({
    String? category,
    String? priority,
    double? minBudget,
    double? maxBudget,
    int page = 1,
  }) async {
    final queryParams = <String, String>{
      'page': page.toString(),
      if (category != null) 'category': category,
      if (priority != null) 'priority': priority,
      if (minBudget != null) 'min_budget': minBudget.toString(),
      if (maxBudget != null) 'max_budget': maxBudget.toString(),
    };

    final queryString = queryParams.entries
        .map((e) => '${e.key}=${e.value}')
        .join('&');

    final response = await ApiService.get(
      '${ApiConfig.availableErrandsEndpoint}?$queryString',
    );
    
    if (response['success'] == true) {
      return ErrandListResponse.fromJson(response);
    } else {
      throw Exception(response['message'] ?? 'Failed to search errands');
    }
  }
}