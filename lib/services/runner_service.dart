import 'dart:io';
import '../models/errand.dart';
import '../models/notification.dart';
import '../config/api_config.dart';
import 'api_service.dart';
import 'errand_api.dart';

class RunnerService {
  static Future<List<Errand>> getAvailableErrands({int page = 1}) async {
    try {
      final response = await ErrandApi.getAvailableErrands(page: page);
      return response.data;
    } catch (e) {
      print('Error fetching available errands: $e');
      rethrow;
    }
  }
  
  static Future<void> acceptErrand(String errandId) async {
    try {
      await ErrandApi.acceptErrand(errandId);
    } catch (e) {
      print('Error accepting errand: $e');
      rethrow;
    }
  }
  
  static Future<List<Errand>> getMyErrands() async {
    try {
      return await ErrandApi.getMyErrands();
    } catch (e) {
      print('Error fetching my errands: $e');
      rethrow;
    }
  }
  
  static Future<void> updateErrandStatus(String errandId, String status) async {
    try {
      await ErrandApi.updateErrandStatus(errandId, status);
    } catch (e) {
      print('Error updating errand status: $e');
      rethrow;
    }
  }
  
  static Future<void> completeErrand(String errandId, {String? notes}) async {
    try {
      await ErrandApi.completeErrand(errandId, notes: notes);
    } catch (e) {
      print('Error completing errand: $e');
      rethrow;
    }
  }
  
  static Future<List<RunnerEarning>> getEarningsHistory() async {
    try {
      return await ErrandApi.getEarningsHistory();
    } catch (e) {
      print('Error fetching earnings history: $e');
      rethrow;
    }
  }

  static Future<List<RunnerEarning>> getEarnings() async {
    try {
      return await ErrandApi.getEarningsHistory();
    } catch (e) {
      print('Error fetching earnings: $e');
      // Return empty list instead of mock data in production
      return [];
    }
  }

  static Future<Map<String, dynamic>> getEarningsStats() async {
    try {
      final response = await ApiService.get('${ApiConfig.runnerEarningsEndpoint}/stats');
      return response;
    } catch (e) {
      print('Error fetching earnings stats: $e');
      // Return empty stats instead of mock data
      return {
        'total_earnings': 0.0,
        'week_earnings': 0.0,
        'month_earnings': 0.0,
        'available_balance': 0.0,
        'completed_jobs': 0,
        'average_rating': 0.0,
        'total_hours': 0,
      };
    }
  }
  
  static Future<void> requestWithdrawal(double amount, String method) async {
    try {
      final request = WithdrawRequest(amount: amount, method: method);
      await ErrandApi.requestWithdrawal(request);
    } catch (e) {
      print('Error requesting withdrawal: $e');
      rethrow;
    }
  }
  
  static Future<void> updateAvailability(List<Map<String, dynamic>> availability) async {
    try {
      await ErrandApi.updateAvailability(availability);
    } catch (e) {
      print('Error updating availability: $e');
      rethrow;
    }
  }
  
  static Future<String> uploadDocument(String filePath) async {
    try {
      final response = await ApiService.postMultipart(
        ApiConfig.runnerDocumentsUploadEndpoint,
        File(filePath),
        'document',
      );
      return response['path'];
    } catch (e) {
      print('Error uploading document: $e');
      rethrow;
    }
  }
  
  static Future<List<Map<String, dynamic>>> getDocuments() async {
    try {
      final response = await ApiService.get(ApiConfig.runnerDocumentsEndpoint);
      if (response['data'] is List) {
        return List<Map<String, dynamic>>.from(response['data']);
      }
      return [];
    } catch (e) {
      print('Error fetching documents: $e');
      rethrow;
    }
  }
  
  static Future<ErrandListResponse> searchErrands({
    String? category,
    String? priority,
    double? minBudget,
    double? maxBudget,
    int page = 1,
  }) async {
    try {
      return await ErrandApi.searchErrands(
        category: category,
        priority: priority,
        minBudget: minBudget,
        maxBudget: maxBudget,
        page: page,
      );
    } catch (e) {
      print('Error searching errands: $e');
      rethrow;
    }
  }
  
  // Notification methods for runners
  static Future<List<AppNotification>> getNotifications() async {
    try {
      final response = await ApiService.get(ApiConfig.notificationsEndpoint);

      // Check if response contains a 'data' field with a list
      if (response['data'] is List) {
        final List<dynamic> notificationsList = response['data'] as List;
        return notificationsList
            .map((json) => AppNotification.fromJson(json as Map<String, dynamic>))
            .toList();
      }

      return [];
    } catch (e) {
      print('Error fetching runner notifications: $e');
      return [];
    }
  }
  
  static Future<void> markNotificationAsRead(String notificationId) async {
    try {
      final endpoint = ApiConfig.markNotificationReadEndpoint.replaceFirst('{id}', notificationId);
      await ApiService.patch(endpoint);
    } catch (e) {
      print('Error marking notification as read: $e');
      rethrow;
    }
  }
}