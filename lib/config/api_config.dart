class ApiConfig {
  static const String baseUrl = 'http://192.168.0.188:8080';
  static const String apiVersion = 'v1';
  
  static String get baseApiUrl => '$baseUrl/api/$apiVersion';
  
  // Auth endpoints - Mobile specific
  static const String loginEndpoint = '/auth/mobile/login';
  static const String registerEndpoint = '/auth/mobile/register';
  static const String refreshTokenEndpoint = '/auth/mobile/refresh-token';
  static const String forgotPasswordEndpoint = '/auth/forgot-password';
  
  // User endpoints
  static const String userProfileEndpoint = '/users/profile';
  static const String userProfilePhotoEndpoint = '/runner/profile/photo';
  
  // Errand endpoints (Client)
  static const String errandsEndpoint = '/errands';
  static const String userErrandsEndpoint = '/errands/user';
  static const String bookingsEndpoint = '/bookings';
  
  // Runner endpoints
  static const String availableErrandsEndpoint = '/errands/available';
  static const String runnerErrandsEndpoint = '/errands/runner';
  static const String acceptErrandEndpoint = '/errands/{id}/accept';
  static const String startErrandEndpoint = '/errands/{id}/start';
  static const String completeErrandEndpoint = '/errands/{id}/complete';
  static const String updateLocationEndpoint = '/errands/{id}/update-location';
  static const String runnerProfileEndpoint = '/runners/profile';
  static const String runnerDocumentsEndpoint = '/runner/documents';
  static const String runnerDocumentsUploadEndpoint = '/runner/documents/upload';
  static const String runnerVerificationStatusEndpoint = '/runner/verification/status';
  static const String runnerPerformanceEndpoint = '/runner/performance/overview';
  
  // Notification endpoints
  static const String notificationsEndpoint = '/notifications';
  static const String markNotificationReadEndpoint = '/notifications/{id}/read';
  static const String deviceTokenEndpoint = '/notifications/device';
  
  // Payment endpoints
  static const String paymentMethodsZimbabweEndpoint = '/payments/methods/zimbabwe';
  static const String ecocashPaymentEndpoint = '/payments/ecocash';
  
  // Rating endpoints
  static const String ratingsEndpoint = '/ratings';
  
  // Headers
  static Map<String, String> get defaultHeaders => {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  };
  
  static Map<String, String> getAuthHeaders(String token) => {
    ...defaultHeaders,
    'Authorization': 'Bearer $token',
  };
}