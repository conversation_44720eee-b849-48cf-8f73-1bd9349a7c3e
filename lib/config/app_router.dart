import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../screens/auth/splash/splash_screen.dart';
import '../screens/auth/login/login_screen.dart';
import '../screens/auth/register/role_selection_screen.dart';
import '../screens/auth/register/basic_info_screen.dart';
import '../screens/auth/register/runner_verification_screen.dart';
import '../screens/auth/register/registration_complete_screen.dart';
import '../screens/auth/forgot_password/forgot_password_screen.dart';
import '../screens/customer/home/<USER>';
import '../screens/runner/dashboard/runner_dashboard_screen.dart';
import '../screens/runner/errands/errands_screen.dart';
import '../screens/common/profile/profile_screen.dart';
import '../screens/common/notifications/notifications_screen.dart';
import '../screens/customer/services/service_categories_screen.dart';
import '../screens/customer/services/service_listings_screen.dart';
import '../screens/customer/services/service_details_screen.dart';
import '../screens/customer/bookings/bookings_screen.dart';
import '../screens/customer/booking/booking_screen.dart';
import '../screens/runner/earnings/earnings_screen.dart';
import '../screens/runner/payouts/payouts_screen.dart';
import '../screens/runner/documents/documents_screen.dart';
import '../screens/runner/availability/availability_screen.dart';
import '../screens/runner/active_errands/active_errands_screen.dart';
import '../screens/common/support/support_hub_screen.dart';
import '../screens/common/support/support_chat_screen.dart';
import '../screens/common/location/live_tracking_screen.dart';
import '../models/user.dart';

class AppRouter {
  static final _rootNavigatorKey = GlobalKey<NavigatorState>();
  static final _shellNavigatorKey = GlobalKey<NavigatorState>();

  static final GoRouter router = GoRouter(
    navigatorKey: _rootNavigatorKey,
    initialLocation: '/',
    routes: [
      // Splash Screen
      GoRoute(
        path: '/',
        builder: (context, state) => const SplashScreen(),
      ),
      
      // Authentication Routes
      GoRoute(
        path: '/login',
        builder: (context, state) => const LoginScreen(),
      ),
      GoRoute(
        path: '/register',
        builder: (context, state) => const RoleSelectionScreen(),
      ),
      GoRoute(
        path: '/register/basic-info',
        builder: (context, state) {
          final role = state.extra as UserRole;
          return BasicInfoScreen(role: role);
        },
      ),
      GoRoute(
        path: '/register/runner-verification',
        builder: (context, state) {
          final registrationData = state.extra as Map<String, dynamic>;
          return RunnerVerificationScreen(registrationData: registrationData);
        },
      ),
      GoRoute(
        path: '/register/complete',
        builder: (context, state) {
          final registrationData = state.extra as Map<String, dynamic>;
          return RegistrationCompleteScreen(registrationData: registrationData);
        },
      ),
      GoRoute(
        path: '/forgot-password',
        builder: (context, state) => const ForgotPasswordScreen(),
      ),
      
      // Customer Routes
      ShellRoute(
        navigatorKey: _shellNavigatorKey,
        builder: (context, state, child) {
          return CustomerShell(child: child);
        },
        routes: [
          GoRoute(
            path: '/customer/home',
            builder: (context, state) => const CustomerHomeScreen(),
          ),
          GoRoute(
            path: '/customer/services',
            builder: (context, state) => const ServiceCategoriesScreen(),
          ),
          GoRoute(
            path: '/customer/services/listings',
            builder: (context, state) {
              final category = state.uri.queryParameters['category'];
              final categoryName = state.uri.queryParameters['name'];
              return ServiceListingsScreen(
                category: category,
                categoryName: categoryName,
              );
            },
          ),
          GoRoute(
            path: '/customer/services/details/:serviceId',
            builder: (context, state) {
              final serviceId = state.pathParameters['serviceId']!;
              return ServiceDetailsScreen(serviceId: serviceId);
            },
          ),
          GoRoute(
            path: '/customer/bookings',
            builder: (context, state) => const BookingsScreen(),
          ),
          GoRoute(
            path: '/customer/booking/:serviceId',
            builder: (context, state) {
              final serviceId = state.pathParameters['serviceId']!;
              return BookingScreen(serviceId: serviceId);
            },
          ),
          GoRoute(
            path: '/customer/profile',
            builder: (context, state) => const ProfileScreen(),
          ),
          GoRoute(
            path: '/customer/notifications',
            builder: (context, state) => const NotificationsScreen(),
          ),
        ],
      ),
      
      // Runner Routes
      GoRoute(
        path: '/runner/dashboard',
        builder: (context, state) => const RunnerDashboardScreen(),
      ),
      GoRoute(
        path: '/runner/errands',
        builder: (context, state) => const ErrandsScreen(),
      ),
      GoRoute(
        path: '/runner/active-errands',
        builder: (context, state) => const ActiveErrandsScreen(),
      ),
      GoRoute(
        path: '/runner/earnings',
        builder: (context, state) => const EarningsScreen(),
      ),
      GoRoute(
        path: '/runner/payouts',
        builder: (context, state) => const PayoutsScreen(),
      ),
      GoRoute(
        path: '/runner/documents',
        builder: (context, state) => const DocumentsScreen(),
      ),
      GoRoute(
        path: '/runner/availability',
        builder: (context, state) => const AvailabilityScreen(),
      ),
      GoRoute(
        path: '/runner/profile',
        builder: (context, state) => const ProfileScreen(),
      ),
      GoRoute(
        path: '/runner/notifications',
        builder: (context, state) => const NotificationsScreen(),
      ),
      
      // Common Support Routes
      GoRoute(
        path: '/support',
        builder: (context, state) => const SupportHubScreen(),
      ),
      GoRoute(
        path: '/support/chat',
        builder: (context, state) => const SupportChatScreen(),
      ),
      GoRoute(
        path: '/live-tracking/:errandId',
        builder: (context, state) {
          final errandId = state.pathParameters['errandId']!;
          final runnerName = state.uri.queryParameters['runnerName'] ?? 'Runner';
          final runnerPhone = state.uri.queryParameters['runnerPhone'];
          return LiveTrackingScreen(
            errandId: errandId,
            runnerName: runnerName,
            runnerPhone: runnerPhone,
          );
        },
      ),
    ],
    redirect: (context, state) async {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('auth_token');
      final userRole = prefs.getString('user_role');
      
      // If user is on splash screen, let it handle navigation
      if (state.matchedLocation == '/') {
        return null;
      }
      
      // If user is not authenticated and trying to access protected routes
      if (token == null && 
          !state.matchedLocation.startsWith('/login') && 
          !state.matchedLocation.startsWith('/register') &&
          !state.matchedLocation.startsWith('/forgot-password')) {
        return '/login';
      }
      
      // If user is authenticated but on auth screens, redirect to dashboard
      if (token != null && (
          state.matchedLocation.startsWith('/login') || 
          state.matchedLocation.startsWith('/register'))) {
        if (userRole == 'client') {
          return '/customer/home';
        } else if (userRole == 'agent') {
          return '/runner/dashboard';
        }
      }
      
      return null;
    },
  );
}

// Customer Shell for bottom navigation
class CustomerShell extends StatefulWidget {
  final Widget child;
  
  const CustomerShell({super.key, required this.child});

  @override
  State<CustomerShell> createState() => _CustomerShellState();
}

class _CustomerShellState extends State<CustomerShell> {
  int _selectedIndex = 0;
  
  final List<NavigationItem> _items = [
    NavigationItem(
      icon: Icons.home_outlined,
      selectedIcon: Icons.home,
      label: 'Home',
      route: '/customer/home',
    ),
    NavigationItem(
      icon: Icons.search_outlined,
      selectedIcon: Icons.search,
      label: 'Services',
      route: '/customer/services',
    ),
    NavigationItem(
      icon: Icons.calendar_today_outlined,
      selectedIcon: Icons.calendar_today,
      label: 'Bookings',
      route: '/customer/bookings',
    ),
    NavigationItem(
      icon: Icons.notifications_outlined,
      selectedIcon: Icons.notifications,
      label: 'Notifications',
      route: '/customer/notifications',
    ),
    NavigationItem(
      icon: Icons.person_outlined,
      selectedIcon: Icons.person,
      label: 'Profile',
      route: '/customer/profile',
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: widget.child,
      bottomNavigationBar: BottomNavigationBar(
        currentIndex: _selectedIndex,
        type: BottomNavigationBarType.fixed,
        selectedItemColor: const Color(0xFF1B365D),
        unselectedItemColor: Colors.grey,
        showUnselectedLabels: true,
        items: _items.map((item) => BottomNavigationBarItem(
          icon: Icon(item.icon),
          activeIcon: Icon(item.selectedIcon),
          label: item.label,
        )).toList(),
        onTap: (index) {
          setState(() {
            _selectedIndex = index;
          });
          context.go(_items[index].route);
        },
      ),
    );
  }
}

class NavigationItem {
  final IconData icon;
  final IconData selectedIcon;
  final String label;
  final String route;
  
  NavigationItem({
    required this.icon,
    required this.selectedIcon,
    required this.label,
    required this.route,
  });
}