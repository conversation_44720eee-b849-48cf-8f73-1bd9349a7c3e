// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        return windows;
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyD7tfSiVSkkBv8NxBR6N21foXHDdlIL_cM',
    appId: '1:954115894535:web:76d4edaaa548f3e4faa9a2',
    messagingSenderId: '954115894535',
    projectId: 'taskrabitinc',
    authDomain: 'taskrabitinc.firebaseapp.com',
    storageBucket: 'taskrabitinc.firebasestorage.app',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyAWBkbulvmE6NDmuO4u4SLFmIhl61Rw-EY',
    appId: '1:954115894535:android:2d2a11493be15867faa9a2',
    messagingSenderId: '954115894535',
    projectId: 'taskrabitinc',
    storageBucket: 'taskrabitinc.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyC7aCUw97NODNAKq0eu9bBb2Y2pzw-aNJk',
    appId: '1:954115894535:ios:897eea557ddc36fffaa9a2',
    messagingSenderId: '954115894535',
    projectId: 'taskrabitinc',
    storageBucket: 'taskrabitinc.firebasestorage.app',
    iosBundleId: 'inc.rabit.task.www',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyC7aCUw97NODNAKq0eu9bBb2Y2pzw-aNJk',
    appId: '1:954115894535:ios:897eea557ddc36fffaa9a2',
    messagingSenderId: '954115894535',
    projectId: 'taskrabitinc',
    storageBucket: 'taskrabitinc.firebasestorage.app',
    iosBundleId: 'inc.rabit.task.www',
  );

  static const FirebaseOptions windows = FirebaseOptions(
    apiKey: 'AIzaSyD7tfSiVSkkBv8NxBR6N21foXHDdlIL_cM',
    appId: '1:954115894535:web:e1be0759e065d06afaa9a2',
    messagingSenderId: '954115894535',
    projectId: 'taskrabitinc',
    authDomain: 'taskrabitinc.firebaseapp.com',
    storageBucket: 'taskrabitinc.firebasestorage.app',
  );
}
