import 'package:flutter/material.dart';

class AppLogo extends StatelessWidget {
  final double? width;
  final double? height;
  final Color? backgroundColor;
  final Color? borderColor;
  final double borderRadius;
  final double borderWidth;
  final EdgeInsets? padding;
  final bool showBorder;
  final bool showBackground;

  const AppLogo({
    super.key,
    this.width,
    this.height,
    this.backgroundColor,
    this.borderColor,
    this.borderRadius = 16.0,
    this.borderWidth = 2.0,
    this.padding,
    this.showBorder = false,
    this.showBackground = false,
  });

  const AppLogo.small({
    super.key,
    this.backgroundColor,
    this.borderColor,
    this.borderRadius = 8.0,
    this.borderWidth = 1.0,
    this.padding,
    this.showBorder = false,
    this.showBackground = false,
  }) : width = 40.0, height = 40.0;

  const AppLogo.medium({
    super.key,
    this.backgroundColor,
    this.borderColor,
    this.borderRadius = 12.0,
    this.borderWidth = 2.0,
    this.padding,
    this.showBorder = false,
    this.showBackground = false,
  }) : width = 60.0, height = 60.0;

  const AppLogo.large({
    super.key,
    this.backgroundColor,
    this.borderColor,
    this.borderRadius = 16.0,
    this.borderWidth = 2.0,
    this.padding,
    this.showBorder = false,
    this.showBackground = false,
  }) : width = 80.0, height = 80.0;

  const AppLogo.splash({
    super.key,
    this.backgroundColor = Colors.white,
    this.borderColor,
    this.borderRadius = 24.0,
    this.borderWidth = 0.0,
    this.showBorder = false,
    this.showBackground = true,
  }) : width = 120.0, height = 120.0, padding = const EdgeInsets.all(16.0);

  @override
  Widget build(BuildContext context) {
    Widget logoImage = Image.asset(
      'assets/icon/logo.png',
      width: width,
      height: height,
      fit: BoxFit.contain,
    );

    if (padding != null) {
      logoImage = Padding(
        padding: padding!,
        child: logoImage,
      );
    }

    if (!showBackground && !showBorder) {
      return logoImage;
    }

    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        color: showBackground ? (backgroundColor ?? Colors.transparent) : null,
        borderRadius: BorderRadius.circular(borderRadius),
        border: showBorder 
          ? Border.all(
              color: borderColor ?? const Color(0xFF1B365D),
              width: borderWidth,
            )
          : null,
        boxShadow: showBackground ? [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ] : null,
      ),
      child: logoImage,
    );
  }
}

class AppLogoWithText extends StatelessWidget {
  final double logoSize;
  final double fontSize;
  final String text;
  final Color? textColor;
  final FontWeight fontWeight;
  final double spacing;
  final MainAxisAlignment alignment;
  final bool isHorizontal;

  const AppLogoWithText({
    super.key,
    this.logoSize = 40.0,
    this.fontSize = 24.0,
    this.text = 'TaskRabbit Inc',
    this.textColor,
    this.fontWeight = FontWeight.bold,
    this.spacing = 12.0,
    this.alignment = MainAxisAlignment.center,
    this.isHorizontal = false,
  });

  const AppLogoWithText.horizontal({
    super.key,
    this.logoSize = 32.0,
    this.fontSize = 20.0,
    this.text = 'TaskRabbit Inc',
    this.textColor,
    this.fontWeight = FontWeight.bold,
    this.spacing = 12.0,
    this.alignment = MainAxisAlignment.center,
  }) : isHorizontal = true;

  @override
  Widget build(BuildContext context) {
    final logo = AppLogo(
      width: logoSize,
      height: logoSize,
    );

    final textWidget = Text(
      text,
      style: TextStyle(
        fontSize: fontSize,
        fontWeight: fontWeight,
        color: textColor ?? const Color(0xFF1B365D),
      ),
    );

    if (isHorizontal) {
      return Row(
        mainAxisAlignment: alignment,
        children: [
          logo,
          SizedBox(width: spacing),
          textWidget,
        ],
      );
    } else {
      return Column(
        mainAxisAlignment: alignment,
        children: [
          logo,
          SizedBox(height: spacing),
          textWidget,
        ],
      );
    }
  }
}
