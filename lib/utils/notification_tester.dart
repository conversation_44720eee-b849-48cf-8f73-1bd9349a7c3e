import 'dart:convert';
import '../services/notification_manager.dart';
import '../services/firebase_messaging_service.dart';
import '../services/api_service.dart';
import '../services/presence_service.dart';

class NotificationTester {
  // Test FCM token generation and registration
  static Future<bool> testTokenGeneration() async {
    try {
      print('🔄 Testing FCM token generation...');
      
      final token = await FirebaseMessagingService.getToken();
      if (token != null && token.isNotEmpty) {
        print('✅ FCM token generated: ${token.substring(0, 20)}...');
        
        // Test token registration with backend
        await _testTokenRegistration(token);
        return true;
      } else {
        print('❌ Failed to generate FCM token');
        return false;
      }
    } catch (e) {
      print('❌ Token generation test failed: $e');
      return false;
    }
  }
  
  // Test token registration with backend
  static Future<bool> _testTokenRegistration(String token) async {
    try {
      print('🔄 Testing token registration with backend...');
      
      final response = await ApiService.post('/notifications/device', {
        'token': token,
        'platform': 'android',
        'app_version': '1.0.0',
      });
      
      if (response['success'] == true) {
        print('✅ FCM token registered with backend successfully');
        return true;
      } else {
        print('❌ Failed to register FCM token: ${response['message']}');
        return false;
      }
    } catch (e) {
      print('❌ Token registration failed: $e');
      return false;
    }
  }
  
  // Test notification permissions
  static Future<bool> testNotificationPermissions() async {
    try {
      print('🔄 Testing notification permissions...');
      
      final hasPermission = await NotificationManager.requestPermissions();
      if (hasPermission) {
        print('✅ Notification permissions granted');
        
        final status = await NotificationManager.getPermissionStatus();
        print('📱 Permission status: $status');
        return true;
      } else {
        print('❌ Notification permissions denied');
        return false;
      }
    } catch (e) {
      print('❌ Permission test failed: $e');
      return false;
    }
  }
  
  // Test topic subscription
  static Future<bool> testTopicSubscription() async {
    try {
      print('🔄 Testing topic subscription...');
      
      // Subscribe to test topics
      await FirebaseMessagingService.subscribeToTopic('test_topic');
      await FirebaseMessagingService.subscribeToTopic('runners');
      
      print('✅ Successfully subscribed to topics');
      
      // Test unsubscription
      await FirebaseMessagingService.unsubscribeFromTopic('test_topic');
      print('✅ Successfully unsubscribed from test topic');
      
      return true;
    } catch (e) {
      print('❌ Topic subscription test failed: $e');
      return false;
    }
  }
  
  // Test sending notification through backend
  static Future<bool> testNotificationSending() async {
    try {
      print('🔄 Testing notification sending through backend...');
      
      // This would require a test endpoint on your backend
      final response = await ApiService.post('/notifications/test', {
        'title': 'Test Notification',
        'body': 'This is a test notification from the app',
        'data': {
          'type': 'test',
          'timestamp': DateTime.now().toIso8601String(),
        }
      });
      
      if (response['success'] == true) {
        print('✅ Test notification sent successfully');
        return true;
      } else {
        print('⚠️  Test notification endpoint not available: ${response['message']}');
        return false;
      }
    } catch (e) {
      print('⚠️  Test notification sending failed (endpoint may not exist): $e');
      return false;
    }
  }
  
  // Test presence system
  static Future<bool> testPresenceSystem() async {
    try {
      print('🔄 Testing presence system...');
      
      // Initialize presence
      await PresenceService.initialize();
      print('✅ Presence service initialized');
      
      // Test online status
      await PresenceService.setOnlineStatus(true);
      print('✅ Online status set');
      
      // Test runner availability (if user is a runner)
      try {
        await PresenceService.setRunnerAvailability(true);
        print('✅ Runner availability set (user is a runner)');
      } catch (e) {
        print('ℹ️  User is not a runner: $e');
      }
      
      return true;
    } catch (e) {
      print('❌ Presence system test failed: $e');
      return false;
    }
  }
  
  // Test real-time runner tracking
  static Future<bool> testRunnerTracking() async {
    try {
      print('🔄 Testing real-time runner tracking...');
      
      // Get online runners (this will test Firestore connection)
      final runnersStream = PresenceService.getOnlineRunners();
      
      // Listen for a few seconds to test the stream
      await for (final runners in runnersStream.take(1)) {
        print('✅ Found ${runners.length} online runners');
        
        for (final runner in runners) {
          print('  - Runner: ${runner.name} (${runner.status.name})');
          if (runner.location != null) {
            print('    Location: ${runner.location!.latitude}, ${runner.location!.longitude}');
          }
        }
        break;
      }
      
      return true;
    } catch (e) {
      print('❌ Runner tracking test failed: $e');
      return false;
    }
  }
  
  // Test notification message handling
  static Future<bool> testMessageHandling() async {
    try {
      print('🔄 Testing message handling...');
      
      // Test different notification types
      final testMessages = [
        {
          'type': 'errand_update',
          'title': 'Errand Status Updated',
          'body': 'Your errand status has been changed to in-progress',
          'data': {'errand_id': 'test_123', 'status': 'in_progress'}
        },
        {
          'type': 'new_errand',
          'title': 'New Errand Available',
          'body': 'A delivery errand is available nearby',
          'data': {'errand_id': 'new_456', 'category': 'delivery'}
        },
        {
          'type': 'payment_update',
          'title': 'Payment Received',
          'body': 'You have received payment for completed errand',
          'data': {'amount': '25.00', 'currency': 'USD'}
        }
      ];
      
      print('✅ Message handling test data prepared');
      print('📝 Test messages: ${testMessages.length}');
      
      return true;
    } catch (e) {
      print('❌ Message handling test failed: $e');
      return false;
    }
  }
  
  // Run all tests
  static Future<Map<String, bool>> runAllTests() async {
    print('🚀 Starting comprehensive notification and presence system tests...\n');
    
    final results = <String, bool>{};
    
    // Test 1: FCM Token Generation
    results['fcm_token'] = await testTokenGeneration();
    await _delay();
    
    // Test 2: Notification Permissions
    results['permissions'] = await testNotificationPermissions();
    await _delay();
    
    // Test 3: Topic Subscription
    results['topics'] = await testTopicSubscription();
    await _delay();
    
    // Test 4: Notification Sending
    results['notification_sending'] = await testNotificationSending();
    await _delay();
    
    // Test 5: Presence System
    results['presence'] = await testPresenceSystem();
    await _delay();
    
    // Test 6: Runner Tracking
    results['runner_tracking'] = await testRunnerTracking();
    await _delay();
    
    // Test 7: Message Handling
    results['message_handling'] = await testMessageHandling();
    
    // Print summary
    _printTestSummary(results);
    
    return results;
  }
  
  // Print test summary
  static void _printTestSummary(Map<String, bool> results) {
    print('\n📊 TEST SUMMARY');
    print('=' * 50);
    
    int passed = 0;
    int total = results.length;
    
    results.forEach((test, success) {
      final status = success ? '✅ PASS' : '❌ FAIL';
      final testName = test.replaceAll('_', ' ').toUpperCase();
      print('$status  $testName');
      if (success) passed++;
    });
    
    print('=' * 50);
    print('📈 Results: $passed/$total tests passed');
    
    final percentage = ((passed / total) * 100).round();
    if (percentage >= 80) {
      print('🎉 Excellent! System is ready for production');
    } else if (percentage >= 60) {
      print('⚠️  Good, but some issues need attention');
    } else {
      print('🔧 Needs significant work before deployment');
    }
  }
  
  // Small delay between tests
  static Future<void> _delay() async {
    await Future.delayed(const Duration(seconds: 1));
  }
  
  // Test specific notification scenario
  static Future<void> testErrandNotificationFlow() async {
    print('🔄 Testing complete errand notification flow...\n');
    
    try {
      // Simulate errand lifecycle notifications
      final scenarios = [
        'Errand Created → Client gets confirmation',
        'Runner Assigned → Client gets runner info',
        'Errand Started → Client gets tracking info',
        'Location Updates → Client gets real-time location',
        'Errand Completed → Both parties get completion notice',
        'Payment Processed → Runner gets payment confirmation'
      ];
      
      for (int i = 0; i < scenarios.length; i++) {
        print('${i + 1}. ${scenarios[i]}');
        await Future.delayed(const Duration(milliseconds: 500));
      }
      
      print('\n✅ Errand notification flow test completed');
    } catch (e) {
      print('❌ Errand notification flow test failed: $e');
    }
  }
}

// Test configuration
class NotificationTestConfig {
  static const Duration testTimeout = Duration(seconds: 30);
  static const bool enableVerboseLogging = true;
  static const List<String> testTopics = ['test', 'runners', 'clients'];
}