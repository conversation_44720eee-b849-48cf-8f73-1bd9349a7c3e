import 'package:flutter/material.dart';
import '../../../services/chat_service.dart';
import '../../../models/chat_message.dart';

class SupportChatScreen extends StatefulWidget {
  const SupportChatScreen({super.key});

  @override
  State<SupportChatScreen> createState() => _SupportChatScreenState();
}

class _SupportChatScreenState extends State<SupportChatScreen> {
  final TextEditingController _messageController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  final FocusNode _messageFocusNode = FocusNode();
  
  bool _isLoading = true;
  bool _isSending = false;
  String? _chatId;
  List<ChatMessage> _messages = [];
  bool _isAgentTyping = false;
  String? _agentName;
  final bool _isChatActive = true;

  @override
  void initState() {
    super.initState();
    _initializeChat();
  }

  @override
  void dispose() {
    _messageController.dispose();
    _scrollController.dispose();
    _messageFocusNode.dispose();
    super.dispose();
  }

  Future<void> _initializeChat() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final chatData = await ChatService.getOrCreateSupportChat();
      setState(() {
        _chatId = chatData['chatId'];
        _messages = List<ChatMessage>.from(chatData['messages'] ?? []);
        _agentName = chatData['agentName'];
        _isLoading = false;
      });

      // Listen for new messages
      _listenForMessages();
      
      // Scroll to bottom after initial load
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _scrollToBottom();
      });

      // Send welcome message if this is a new chat
      if (_messages.isEmpty) {
        await _sendWelcomeMessage();
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to initialize chat: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  Future<void> _sendWelcomeMessage() async {
    // Simulate agent typing
    setState(() {
      _isAgentTyping = true;
    });

    await Future.delayed(const Duration(seconds: 2));

    final welcomeMessage = ChatMessage(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      senderId: 'support_agent',
      senderName: 'TaskRabbit Support',
      senderAvatar: null,
      content: 'Hi! I\'m here to help you with any questions or issues you might have. How can I assist you today?',
      timestamp: DateTime.now(),
      type: MessageType.text,
      isFromSupport: true,
    );

    setState(() {
      _messages.add(welcomeMessage);
      _isAgentTyping = false;
    });

    await ChatService.saveChatMessage(welcomeMessage);
    _scrollToBottom();
  }

  void _listenForMessages() {
    // In a real app, this would listen to Firestore or WebSocket
    // For demo purposes, we'll simulate agent responses
  }

  Future<void> _sendMessage() async {
    final content = _messageController.text.trim();
    if (content.isEmpty || _isSending) return;

    setState(() {
      _isSending = true;
    });

    final message = ChatMessage(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      senderId: 'current_user',
      senderName: 'You',
      senderAvatar: null,
      content: content,
      timestamp: DateTime.now(),
      type: MessageType.text,
      isFromSupport: false,
    );

    setState(() {
      _messages.add(message);
      _isSending = false;
    });

    await ChatService.saveChatMessage(message);
    _messageController.clear();
    _scrollToBottom();

    // Simulate agent response after a delay
    _simulateAgentResponse(content);
  }

  Future<void> _simulateAgentResponse(String userMessage) async {
    // Show typing indicator
    setState(() {
      _isAgentTyping = true;
    });

    // Simulate processing time
    await Future.delayed(const Duration(seconds: 2, milliseconds: 500));

    String response = _generateAgentResponse(userMessage.toLowerCase());

    final agentMessage = ChatMessage(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      senderId: 'support_agent',
      senderName: _agentName ?? 'TaskRabbit Support',
      senderAvatar: null,
      content: response,
      timestamp: DateTime.now(),
      type: MessageType.text,
      isFromSupport: true,
    );

    setState(() {
      _messages.add(agentMessage);
      _isAgentTyping = false;
    });

    await ChatService.saveChatMessage(agentMessage);
    _scrollToBottom();
  }

  String _generateAgentResponse(String userMessage) {
    // Simple response system - in real app this would be more sophisticated
    if (userMessage.contains('payment') || userMessage.contains('pay') || userMessage.contains('charge')) {
      return 'I can help you with payment issues. Can you please provide more details about the specific payment problem you\'re experiencing?';
    } else if (userMessage.contains('booking') || userMessage.contains('book') || userMessage.contains('appointment')) {
      return 'I\'d be happy to help with your booking. Are you having trouble making a new booking or do you need to modify an existing one?';
    } else if (userMessage.contains('cancel') || userMessage.contains('refund')) {
      return 'I understand you need help with a cancellation. Please share your booking ID and I\'ll look into your refund options.';
    } else if (userMessage.contains('runner') || userMessage.contains('tasker')) {
      return 'Are you having an issue with a runner/tasker? I can help connect you with them or assist with any service-related concerns.';
    } else if (userMessage.contains('account') || userMessage.contains('profile') || userMessage.contains('login')) {
      return 'I can assist with account-related issues. Are you having trouble logging in or do you need help updating your profile information?';
    } else if (userMessage.contains('hello') || userMessage.contains('hi') || userMessage.contains('hey')) {
      return 'Hello! Thanks for reaching out. What can I help you with today?';
    } else if (userMessage.contains('thank') || userMessage.contains('thanks')) {
      return 'You\'re welcome! Is there anything else I can help you with?';
    } else {
      return 'I\'m here to help! Could you please provide a bit more detail about your question or concern so I can assist you better?';
    }
  }

  void _scrollToBottom() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      appBar: AppBar(
        title: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'TaskRabbit Support',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            if (_agentName != null)
              Text(
                'Chat with $_agentName',
                style: const TextStyle(fontSize: 12, fontWeight: FontWeight.normal),
              ),
          ],
        ),
        backgroundColor: const Color(0xFF1B365D),
        foregroundColor: Colors.white,
        actions: [
          PopupMenuButton<String>(
            onSelected: _handleMenuAction,
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'transcript',
                child: Row(
                  children: [
                    Icon(Icons.email, size: 20),
                    SizedBox(width: 8),
                    Text('Email Transcript'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'escalate',
                child: Row(
                  children: [
                    Icon(Icons.supervisor_account, size: 20),
                    SizedBox(width: 8),
                    Text('Escalate to Manager'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'end',
                child: Row(
                  children: [
                    Icon(Icons.call_end, size: 20),
                    SizedBox(width: 8),
                    Text('End Chat'),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Column(
              children: [
                // Chat status bar
                if (_isChatActive) _buildChatStatusBar(),
                
                // Messages
                Expanded(
                  child: ListView.builder(
                    controller: _scrollController,
                    padding: const EdgeInsets.all(16),
                    itemCount: _messages.length + (_isAgentTyping ? 1 : 0),
                    itemBuilder: (context, index) {
                      if (index == _messages.length && _isAgentTyping) {
                        return _buildTypingIndicator();
                      }
                      
                      final message = _messages[index];
                      return _buildMessageBubble(message);
                    },
                  ),
                ),
                
                // Message input
                _buildMessageInput(),
              ],
            ),
    );
  }

  Widget _buildChatStatusBar() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      color: Colors.green.shade50,
      child: Row(
        children: [
          Container(
            width: 8,
            height: 8,
            decoration: const BoxDecoration(
              color: Colors.green,
              shape: BoxShape.circle,
            ),
          ),
          const SizedBox(width: 8),
          const Text(
            'Connected to support',
            style: TextStyle(
              fontSize: 12,
              color: Colors.green,
              fontWeight: FontWeight.w600,
            ),
          ),
          const Spacer(),
          const Text(
            'Typically responds in a few minutes',
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMessageBubble(ChatMessage message) {
    final isFromUser = !message.isFromSupport;
    
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: Row(
        mainAxisAlignment: isFromUser ? MainAxisAlignment.end : MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (!isFromUser) ...[
            CircleAvatar(
              radius: 16,
              backgroundColor: const Color(0xFF1B365D),
              child: Text(
                message.senderName[0].toUpperCase(),
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            const SizedBox(width: 8),
          ],
          Flexible(
            child: Container(
              constraints: BoxConstraints(
                maxWidth: MediaQuery.of(context).size.width * 0.75,
              ),
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              decoration: BoxDecoration(
                color: isFromUser ? const Color(0xFF1B365D) : Colors.white,
                borderRadius: BorderRadius.only(
                  topLeft: const Radius.circular(16),
                  topRight: const Radius.circular(16),
                  bottomLeft: Radius.circular(isFromUser ? 16 : 4),
                  bottomRight: Radius.circular(isFromUser ? 4 : 16),
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.05),
                    blurRadius: 5,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  if (!isFromUser && message.senderName != 'TaskRabbit Support')
                    Padding(
                      padding: const EdgeInsets.only(bottom: 4),
                      child: Text(
                        message.senderName,
                        style: const TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                          color: Color(0xFF1B365D),
                        ),
                      ),
                    ),
                  Text(
                    message.content,
                    style: TextStyle(
                      fontSize: 16,
                      color: isFromUser ? Colors.white : Colors.black87,
                      height: 1.4,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    _formatMessageTime(message.timestamp),
                    style: TextStyle(
                      fontSize: 11,
                      color: isFromUser ? Colors.white70 : Colors.grey.shade600,
                    ),
                  ),
                ],
              ),
            ),
          ),
          if (isFromUser) ...[
            const SizedBox(width: 8),
            CircleAvatar(
              radius: 16,
              backgroundColor: Colors.grey.shade300,
              child: const Icon(
                Icons.person,
                size: 16,
                color: Colors.grey,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildTypingIndicator() {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: Row(
        children: [
          CircleAvatar(
            radius: 16,
            backgroundColor: const Color(0xFF1B365D),
            child: Text(
              _agentName?[0].toUpperCase() ?? 'S',
              style: const TextStyle(
                color: Colors.white,
                fontSize: 12,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          const SizedBox(width: 8),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(16),
                topRight: Radius.circular(16),
                bottomLeft: Radius.circular(4),
                bottomRight: Radius.circular(16),
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  blurRadius: 5,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: const _TypingAnimation(),
          ),
        ],
      ),
    );
  }

  Widget _buildMessageInput() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        children: [
          IconButton(
            onPressed: _showAttachmentOptions,
            icon: const Icon(Icons.attach_file),
            color: Colors.grey.shade600,
          ),
          Expanded(
            child: TextField(
              controller: _messageController,
              focusNode: _messageFocusNode,
              decoration: InputDecoration(
                hintText: 'Type your message...',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(24),
                  borderSide: BorderSide(color: Colors.grey.shade300),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(24),
                  borderSide: BorderSide(color: Colors.grey.shade300),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(24),
                  borderSide: const BorderSide(color: Color(0xFF1B365D)),
                ),
                contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              ),
              maxLines: null,
              textInputAction: TextInputAction.send,
              onSubmitted: (_) => _sendMessage(),
            ),
          ),
          const SizedBox(width: 8),
          Material(
            color: const Color(0xFF1B365D),
            borderRadius: BorderRadius.circular(24),
            child: InkWell(
              borderRadius: BorderRadius.circular(24),
              onTap: _isSending ? null : _sendMessage,
              child: Container(
                padding: const EdgeInsets.all(12),
                child: _isSending
                    ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      )
                    : const Icon(
                        Icons.send,
                        color: Colors.white,
                        size: 20,
                      ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  String _formatMessageTime(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inDays > 0) {
      return '${timestamp.day}/${timestamp.month}/${timestamp.year}';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }

  void _showAttachmentOptions() {
    showModalBottomSheet(
      context: context,
      builder: (context) => SafeArea(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Padding(
              padding: EdgeInsets.all(16),
              child: Text(
                'Send Attachment',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            ListTile(
              leading: const Icon(Icons.photo_camera),
              title: const Text('Camera'),
              onTap: () {
                Navigator.pop(context);
                _sendAttachment('camera');
              },
            ),
            ListTile(
              leading: const Icon(Icons.photo_library),
              title: const Text('Photo Gallery'),
              onTap: () {
                Navigator.pop(context);
                _sendAttachment('gallery');
              },
            ),
            ListTile(
              leading: const Icon(Icons.insert_drive_file),
              title: const Text('Document'),
              onTap: () {
                Navigator.pop(context);
                _sendAttachment('document');
              },
            ),
            const SizedBox(height: 16),
          ],
        ),
      ),
    );
  }

  void _sendAttachment(String type) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('$type attachment feature coming soon')),
    );
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'transcript':
        _emailTranscript();
        break;
      case 'escalate':
        _escalateToManager();
        break;
      case 'end':
        _endChat();
        break;
    }
  }

  void _emailTranscript() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Chat transcript will be emailed to you')),
    );
  }

  void _escalateToManager() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Escalate to Manager'),
        content: const Text('Would you like to escalate this chat to a manager? This may increase response time but ensures specialized assistance.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _simulateEscalation();
            },
            child: const Text('Escalate'),
          ),
        ],
      ),
    );
  }

  void _simulateEscalation() async {
    setState(() {
      _agentName = 'Manager Sarah';
    });

    await Future.delayed(const Duration(seconds: 1));

    final escalationMessage = ChatMessage(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      senderId: 'manager_sarah',
      senderName: 'Manager Sarah',
      senderAvatar: null,
      content: 'Hi, I\'m Sarah, a manager here at TaskRabbit. I understand you need additional assistance. How can I help resolve this for you?',
      timestamp: DateTime.now(),
      type: MessageType.text,
      isFromSupport: true,
    );

    setState(() {
      _messages.add(escalationMessage);
    });

    await ChatService.saveChatMessage(escalationMessage);
    _scrollToBottom();
  }

  void _endChat() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('End Chat'),
        content: const Text('Are you sure you want to end this chat session? You can always start a new chat if you need more help.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              Navigator.pop(context);
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('End Chat'),
          ),
        ],
      ),
    );
  }
}

class _TypingAnimation extends StatefulWidget {
  const _TypingAnimation();

  @override
  State<_TypingAnimation> createState() => _TypingAnimationState();
}

class _TypingAnimationState extends State<_TypingAnimation>
    with TickerProviderStateMixin {
  late AnimationController _animationController;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    )..repeat();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        const Text(
          'TaskRabbit is typing',
          style: TextStyle(
            fontSize: 14,
            color: Colors.grey,
            fontStyle: FontStyle.italic,
          ),
        ),
        const SizedBox(width: 8),
        AnimatedBuilder(
          animation: _animationController,
          builder: (context, child) {
            return Row(
              children: List.generate(3, (index) {
                final delay = index * 0.2;
                final animValue = (_animationController.value - delay).clamp(0.0, 1.0);
                final scale = (1.0 + 0.5 * (1.0 - (animValue * 2 - 1).abs()));
                
                return Transform.scale(
                  scale: scale,
                  child: Container(
                    width: 4,
                    height: 4,
                    margin: const EdgeInsets.symmetric(horizontal: 1),
                    decoration: const BoxDecoration(
                      color: Colors.grey,
                      shape: BoxShape.circle,
                    ),
                  ),
                );
              }),
            );
          },
        ),
      ],
    );
  }
}