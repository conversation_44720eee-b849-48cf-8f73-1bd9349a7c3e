import 'package:flutter/material.dart';
import 'dart:async';
import 'dart:math' as math;
import '../../../services/location_service.dart';
import '../../../models/location_data.dart';

class LiveTrackingScreen extends StatefulWidget {
  final String errandId;
  final String runnerName;
  final String? runnerPhone;
  final String? runnerAvatar;

  const LiveTrackingScreen({
    super.key,
    required this.errandId,
    required this.runnerName,
    this.runnerPhone,
    this.runnerAvatar,
  });

  @override
  State<LiveTrackingScreen> createState() => _LiveTrackingScreenState();
}

class _LiveTrackingScreenState extends State<LiveTrackingScreen>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late Animation<double> _pulseAnimation;
  
  StreamSubscription<LocationUpdate>? _locationSubscription;
  LocationUpdate? _currentLocation;
  bool _isLoading = true;
  bool _isTrackingActive = false;
  String _estimatedArrival = '';
  double _distanceToCustomer = 0.0;
  
  // Mock customer location (in real app, this would come from the booking)
  final LocationData _customerLocation = LocationData(
    latitude: 40.7589,
    longitude: -73.9851,
    address: '123 Main Street, New York, NY 10001',
  );

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _initializeTracking();
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _locationSubscription?.cancel();
    super.dispose();
  }

  void _setupAnimations() {
    _pulseController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );
    
    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.3,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));
    
    _pulseController.repeat(reverse: true);
  }

  Future<void> _initializeTracking() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Start location tracking
      _locationSubscription = LocationService.trackRunnerLocation(widget.errandId)
          .listen(_onLocationUpdate);
      
      // Get initial location
      final initialLocation = await LocationService.getRunnerLocation(widget.errandId);
      _onLocationUpdate(initialLocation);
      
      setState(() {
        _isLoading = false;
        _isTrackingActive = true;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to start tracking: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _onLocationUpdate(LocationUpdate update) {
    setState(() {
      _currentLocation = update;
      _distanceToCustomer = _calculateDistance(
        update.location,
        _customerLocation,
      );
      _estimatedArrival = _calculateETA(_distanceToCustomer);
    });
  }

  double _calculateDistance(LocationData from, LocationData to) {
    // Simplified distance calculation - in real app use proper geolocation
    const double earthRadiusKm = 6371;
    final double lat1Rad = from.latitude * (3.14159 / 180);
    final double lat2Rad = to.latitude * (3.14159 / 180);
    final double deltaLatRad = (to.latitude - from.latitude) * (3.14159 / 180);
    final double deltaLngRad = (to.longitude - from.longitude) * (3.14159 / 180);

    final double a = math.pow(deltaLatRad / 2, 2) +
        math.cos(lat1Rad) * math.cos(lat2Rad) *
        math.pow(deltaLngRad / 2, 2);

    final double c = 2 * math.asin(math.sqrt(a));
    return earthRadiusKm * c;
  }

  String _calculateETA(double distanceKm) {
    // Assume average speed of 30 km/h in urban areas
    final double hoursToArrival = distanceKm / 30;
    final int minutesToArrival = (hoursToArrival * 60).round();
    
    if (minutesToArrival < 5) {
      return 'Arriving soon';
    } else if (minutesToArrival < 60) {
      return '$minutesToArrival min';
    } else {
      final int hours = minutesToArrival ~/ 60;
      final int minutes = minutesToArrival % 60;
      return '${hours}h ${minutes}m';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      appBar: AppBar(
        title: Text('Tracking ${widget.runnerName}'),
        backgroundColor: const Color(0xFF1B365D),
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _initializeTracking,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Column(
              children: [
                // Status header
                _buildStatusHeader(),
                
                // Map container (placeholder)
                Expanded(
                  child: _buildMapContainer(),
                ),
                
                // Bottom actions
                _buildBottomActions(),
              ],
            ),
    );
  }

  Widget _buildStatusHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // Runner info
          Row(
            children: [
              CircleAvatar(
                radius: 25,
                backgroundColor: const Color(0xFF1B365D),
                child: Text(
                  widget.runnerName[0].toUpperCase(),
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      widget.runnerName,
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF1B365D),
                      ),
                    ),
                    const SizedBox(height: 4),
                    Row(
                      children: [
                        AnimatedBuilder(
                          animation: _pulseAnimation,
                          builder: (context, child) {
                            return Transform.scale(
                              scale: _pulseAnimation.value,
                              child: Container(
                                width: 8,
                                height: 8,
                                decoration: BoxDecoration(
                                  color: _isTrackingActive ? Colors.green : Colors.grey,
                                  shape: BoxShape.circle,
                                ),
                              ),
                            );
                          },
                        ),
                        const SizedBox(width: 8),
                        Text(
                          _isTrackingActive ? 'Live tracking active' : 'Tracking unavailable',
                          style: TextStyle(
                            fontSize: 14,
                            color: _isTrackingActive ? Colors.green : Colors.grey,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              if (widget.runnerPhone != null)
                IconButton(
                  onPressed: _callRunner,
                  icon: const Icon(Icons.phone),
                  style: IconButton.styleFrom(
                    backgroundColor: Colors.green.shade50,
                    foregroundColor: Colors.green,
                  ),
                ),
            ],
          ),
          
          const SizedBox(height: 20),
          
          // Location stats
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  'Distance',
                  '${_distanceToCustomer.toStringAsFixed(1)} km',
                  Icons.straighten,
                  Colors.blue,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildStatCard(
                  'ETA',
                  _estimatedArrival,
                  Icons.schedule,
                  Colors.orange,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildStatCard(
                  'Speed',
                  _currentLocation?.speed != null 
                      ? '${_currentLocation!.speed.toStringAsFixed(0)} km/h'
                      : '-- km/h',
                  Icons.speed,
                  Colors.purple,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard(String label, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMapContainer() {
    return Container(
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(16),
        child: Stack(
          children: [
            // Map placeholder
            Container(
              width: double.infinity,
              height: double.infinity,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    Colors.blue.shade100,
                    Colors.green.shade100,
                  ],
                ),
              ),
              child: Stack(
                children: [
                  // Grid pattern to simulate map
                  CustomPaint(
                    size: Size.infinite,
                    painter: _MapGridPainter(),
                  ),
                  
                  // Runner location marker
                  if (_currentLocation != null) ...[
                    Positioned(
                      left: 150,
                      top: 200,
                      child: AnimatedBuilder(
                        animation: _pulseController,
                        builder: (context, child) {
                          return Container(
                            width: 40 * _pulseAnimation.value,
                            height: 40 * _pulseAnimation.value,
                            decoration: BoxDecoration(
                              color: Colors.blue.withOpacity(0.2),
                              shape: BoxShape.circle,
                            ),
                            child: Center(
                              child: Container(
                                width: 16,
                                height: 16,
                                decoration: const BoxDecoration(
                                  color: Colors.blue,
                                  shape: BoxShape.circle,
                                ),
                                child: const Icon(
                                  Icons.person,
                                  size: 10,
                                  color: Colors.white,
                                ),
                              ),
                            ),
                          );
                        },
                      ),
                    ),
                  ],
                  
                  // Customer location marker
                  const Positioned(
                    right: 100,
                    bottom: 150,
                    child: Column(
                      children: [
                        Icon(
                          Icons.location_on,
                          color: Colors.red,
                          size: 30,
                        ),
                        Text(
                          'You',
                          style: TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                            color: Colors.red,
                          ),
                        ),
                      ],
                    ),
                  ),
                  
                  // Route line (simplified)
                  Positioned.fill(
                    child: CustomPaint(
                      painter: _RoutePainter(),
                    ),
                  ),
                ],
              ),
            ),
            
            // Map controls
            Positioned(
              top: 16,
              right: 16,
              child: Column(
                children: [
                  FloatingActionButton(
                    mini: true,
                    heroTag: 'zoom_in',
                    backgroundColor: Colors.white,
                    foregroundColor: const Color(0xFF1B365D),
                    onPressed: () {},
                    child: const Icon(Icons.add),
                  ),
                  const SizedBox(height: 8),
                  FloatingActionButton(
                    mini: true,
                    heroTag: 'zoom_out',
                    backgroundColor: Colors.white,
                    foregroundColor: const Color(0xFF1B365D),
                    onPressed: () {},
                    child: const Icon(Icons.remove),
                  ),
                  const SizedBox(height: 8),
                  FloatingActionButton(
                    mini: true,
                    heroTag: 'center',
                    backgroundColor: Colors.white,
                    foregroundColor: const Color(0xFF1B365D),
                    onPressed: _centerOnRunner,
                    child: const Icon(Icons.my_location),
                  ),
                ],
              ),
            ),
            
            // Legend
            Positioned(
              bottom: 16,
              left: 16,
              child: Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.9),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        const Icon(Icons.person, color: Colors.blue, size: 16),
                        const SizedBox(width: 8),
                        Text(
                          widget.runnerName,
                          style: const TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 4),
                    const Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(Icons.location_on, color: Colors.red, size: 16),
                        SizedBox(width: 8),
                        Text(
                          'Your Location',
                          style: TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBottomActions() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Column(
        children: [
          // Last update info
          if (_currentLocation != null)
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.green.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.green.shade200),
              ),
              child: Row(
                children: [
                  Icon(Icons.access_time, color: Colors.green.shade700, size: 16),
                  const SizedBox(width: 8),
                  Text(
                    'Last updated: ${_formatTime(_currentLocation!.timestamp)}',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.green.shade700,
                    ),
                  ),
                ],
              ),
            ),
          
          const SizedBox(height: 16),
          
          // Action buttons
          Row(
            children: [
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: _shareLocation,
                  icon: const Icon(Icons.share_location),
                  label: const Text('Share Location'),
                  style: OutlinedButton.styleFrom(
                    foregroundColor: const Color(0xFF1B365D),
                    side: const BorderSide(color: Color(0xFF1B365D)),
                    padding: const EdgeInsets.symmetric(vertical: 12),
                  ),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: widget.runnerPhone != null ? _callRunner : null,
                  icon: const Icon(Icons.phone),
                  label: const Text('Call Runner'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.green,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 12),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  String _formatTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);
    
    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes} min ago';
    } else {
      final hour = dateTime.hour > 12 ? dateTime.hour - 12 : (dateTime.hour == 0 ? 12 : dateTime.hour);
      final minute = dateTime.minute.toString().padLeft(2, '0');
      final period = dateTime.hour >= 12 ? 'PM' : 'AM';
      return '$hour:$minute $period';
    }
  }

  void _centerOnRunner() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Centering on runner location')),
    );
  }

  void _shareLocation() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Sharing tracking link...')),
    );
  }

  void _callRunner() {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Calling ${widget.runnerName}...')),
    );
  }
}

class _MapGridPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.white.withOpacity(0.3)
      ..strokeWidth = 1;

    // Draw grid lines
    for (double i = 0; i < size.width; i += 30) {
      canvas.drawLine(Offset(i, 0), Offset(i, size.height), paint);
    }
    for (double i = 0; i < size.height; i += 30) {
      canvas.drawLine(Offset(0, i), Offset(size.width, i), paint);
    }
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}

class _RoutePainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.blue.withOpacity(0.7)
      ..strokeWidth = 3
      ..strokeCap = StrokeCap.round;

    // Draw a curved route between runner and customer
    final path = Path();
    path.moveTo(150, 200); // Runner position
    path.quadraticBezierTo(
      size.width * 0.7, size.height * 0.3, // Control point
      size.width - 100, size.height - 150, // Customer position
    );
    
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}