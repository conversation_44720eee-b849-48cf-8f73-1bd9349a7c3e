import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../../../models/auth_response.dart';
import '../../../models/user.dart';
import '../../../services/auth_service.dart';
import '../../../widgets/common/loading_button.dart';

class RegistrationCompleteScreen extends StatefulWidget {
  final Map<String, dynamic> registrationData;
  
  const RegistrationCompleteScreen({
    super.key,
    required this.registrationData,
  });

  @override
  State<RegistrationCompleteScreen> createState() => _RegistrationCompleteScreenState();
}

class _RegistrationCompleteScreenState extends State<RegistrationCompleteScreen> {
  bool _isLoading = false;
  String? _errorMessage;
  bool _registrationSuccess = false;

  @override
  void initState() {
    super.initState();
    _submitRegistration();
  }

  Future<void> _submitRegistration() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // Split the full name into first and last names
      final fullName = widget.registrationData['name'] as String;
      final nameParts = fullName.trim().split(' ');
      final firstName = nameParts.first;
      final lastName = nameParts.length > 1 ? nameParts.sublist(1).join(' ') : '';

      final registerRequest = RegisterRequest(
        firstName: firstName,
        lastName: lastName,
        email: widget.registrationData['email'],
        password: widget.registrationData['password'],
        passwordConfirmation: widget.registrationData['password'],
        role: widget.registrationData['role'],
        phone: widget.registrationData['phone'],
      );

      final response = await AuthService.register(registerRequest);

      if (response.success) {
        setState(() {
          _registrationSuccess = true;
        });
      } else {
        setState(() {
          _errorMessage = response.message.isNotEmpty ? response.message : 'Registration failed';
        });
      }
    } catch (e) {
      setState(() {
        _errorMessage = e.toString().replaceFirst('Exception: ', '');
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _proceedToLogin() {
    context.go('/login');
  }

  @override
  Widget build(BuildContext context) {
    final role = widget.registrationData['role'] as UserRole;
    
    return Scaffold(
      backgroundColor: Colors.white,
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              if (_isLoading) ...[
                const CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF1B365D)),
                ),
                const SizedBox(height: 24),
                const Text(
                  'Creating your account...',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w500,
                    color: Color(0xFF1B365D),
                  ),
                ),
                const SizedBox(height: 12),
                Text(
                  'Please wait while we set up your ${role.displayName.toLowerCase()} account.',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey.shade600,
                  ),
                ),
              ] else if (_registrationSuccess) ...[
                Container(
                  width: 100,
                  height: 100,
                  decoration: BoxDecoration(
                    color: Colors.green.shade100,
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    Icons.check_rounded,
                    size: 60,
                    color: Colors.green.shade600,
                  ),
                ),
                const SizedBox(height: 32),
                const Text(
                  'Account Created Successfully!',
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF1B365D),
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 16),
                Text(
                  'Welcome to TaskRabbit Inc! Please check your email for verification instructions.',
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.grey.shade600,
                    height: 1.5,
                  ),
                  textAlign: TextAlign.center,
                ),
                if (role == UserRole.agent) ...[
                  const SizedBox(height: 24),
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.orange.shade50,
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(color: Colors.orange.shade200),
                    ),
                    child: Column(
                      children: [
                        Icon(
                          Icons.hourglass_empty,
                          color: Colors.orange.shade600,
                          size: 32,
                        ),
                        const SizedBox(height: 12),
                        Text(
                          'Verification Pending',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                            color: Colors.orange.shade800,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'Our team will review your runner application within 24-48 hours. You\'ll receive an email once approved.',
                          style: TextStyle(
                            fontSize: 13,
                            color: Colors.orange.shade700,
                            height: 1.4,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  ),
                ],
                const SizedBox(height: 48),
                LoadingButton(
                  onPressed: _proceedToLogin,
                  isLoading: false,
                  text: 'Continue to Login',
                ),
              ] else if (_errorMessage != null) ...[
                Container(
                  width: 100,
                  height: 100,
                  decoration: BoxDecoration(
                    color: Colors.red.shade100,
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    Icons.error_outline,
                    size: 60,
                    color: Colors.red.shade600,
                  ),
                ),
                const SizedBox(height: 32),
                const Text(
                  'Registration Failed',
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF1B365D),
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 16),
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.red.shade50,
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: Colors.red.shade200),
                  ),
                  child: Text(
                    _errorMessage!,
                    style: TextStyle(
                      color: Colors.red.shade700,
                      fontSize: 14,
                      height: 1.4,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
                const SizedBox(height: 32),
                Row(
                  children: [
                    Expanded(
                      child: OutlinedButton(
                        onPressed: () => context.pop(),
                        style: OutlinedButton.styleFrom(
                          side: const BorderSide(color: Color(0xFF1B365D)),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                          padding: const EdgeInsets.symmetric(vertical: 16),
                        ),
                        child: const Text(
                          'Go Back',
                          style: TextStyle(
                            color: Color(0xFF1B365D),
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: LoadingButton(
                        onPressed: _submitRegistration,
                        isLoading: false,
                        text: 'Try Again',
                      ),
                    ),
                  ],
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }
}