import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class PayoutsScreen extends StatefulWidget {
  const PayoutsScreen({super.key});

  @override
  State<PayoutsScreen> createState() => _PayoutsScreenState();
}

class _PayoutsScreenState extends State<PayoutsScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  bool _isLoading = true;
  
  double _availableBalance = 0.0;
  double _pendingAmount = 0.0;
  List<PayoutRecord> _payoutHistory = [];
  List<WithdrawalMethod> _withdrawalMethods = [];
  String? _defaultMethod;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _loadPayoutData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadPayoutData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // In a real app, these would be API calls
      final payoutData = await _getMockPayoutData();
      
      setState(() {
        _availableBalance = payoutData['available_balance'];
        _pendingAmount = payoutData['pending_amount'];
        _payoutHistory = payoutData['history'];
        _withdrawalMethods = payoutData['methods'];
        _defaultMethod = payoutData['default_method'];
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to load payout data: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  Future<Map<String, dynamic>> _getMockPayoutData() async {
    // Simulate API delay
    await Future.delayed(const Duration(milliseconds: 800));
    
    return {
      'available_balance': 1245.80,
      'pending_amount': 156.45,
      'default_method': 'bank_account',
      'methods': [
        WithdrawalMethod(
          id: 'bank_account',
          type: 'Bank Account',
          name: 'Wells Fargo ****4567',
          icon: Icons.account_balance,
          isDefault: true,
          processingTime: '1-3 business days',
          fee: 0.0,
        ),
        WithdrawalMethod(
          id: 'paypal',
          type: 'PayPal',
          name: '<EMAIL>',
          icon: Icons.paypal,
          isDefault: false,
          processingTime: 'Instant',
          fee: 2.50,
        ),
        WithdrawalMethod(
          id: 'venmo',
          type: 'Venmo',
          name: '@john-doe-123',
          icon: Icons.mobile_friendly,
          isDefault: false,
          processingTime: 'Instant',
          fee: 1.75,
        ),
      ],
      'history': [
        PayoutRecord(
          id: 1,
          amount: 425.75,
          method: 'Bank Account',
          status: PayoutStatus.completed,
          requestDate: DateTime.now().subtract(const Duration(days: 5)),
          completedDate: DateTime.now().subtract(const Duration(days: 2)),
          fee: 0.0,
          transactionId: 'TXN-2024-001',
        ),
        PayoutRecord(
          id: 2,
          amount: 180.50,
          method: 'PayPal',
          status: PayoutStatus.processing,
          requestDate: DateTime.now().subtract(const Duration(days: 1)),
          fee: 2.50,
          transactionId: 'TXN-2024-002',
        ),
        PayoutRecord(
          id: 3,
          amount: 325.00,
          method: 'Bank Account',
          status: PayoutStatus.completed,
          requestDate: DateTime.now().subtract(const Duration(days: 12)),
          completedDate: DateTime.now().subtract(const Duration(days: 8)),
          fee: 0.0,
          transactionId: 'TXN-2024-003',
        ),
        PayoutRecord(
          id: 4,
          amount: 95.25,
          method: 'Venmo',
          status: PayoutStatus.failed,
          requestDate: DateTime.now().subtract(const Duration(days: 15)),
          fee: 1.75,
          transactionId: 'TXN-2024-004',
          failureReason: 'Insufficient funds in account',
        ),
        PayoutRecord(
          id: 5,
          amount: 567.80,
          method: 'Bank Account',
          status: PayoutStatus.completed,
          requestDate: DateTime.now().subtract(const Duration(days: 20)),
          completedDate: DateTime.now().subtract(const Duration(days: 17)),
          fee: 0.0,
          transactionId: 'TXN-2024-005',
        ),
      ],
    };
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      appBar: AppBar(
        title: const Text('Payouts'),
        backgroundColor: const Color(0xFF1B365D),
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadPayoutData,
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          indicatorColor: Colors.white,
          tabs: const [
            Tab(text: 'Withdraw'),
            Tab(text: 'History'),
          ],
        ),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : TabBarView(
              controller: _tabController,
              children: [
                _buildWithdrawTab(),
                _buildHistoryTab(),
              ],
            ),
    );
  }

  Widget _buildWithdrawTab() {
    return RefreshIndicator(
      onRefresh: _loadPayoutData,
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Balance Overview
            _buildBalanceCard(),
            const SizedBox(height: 24),
            
            // Quick Withdraw Actions
            _buildQuickWithdrawActions(),
            const SizedBox(height: 24),
            
            // Withdrawal Methods
            _buildWithdrawalMethods(),
            const SizedBox(height: 24),
            
            // Custom Withdrawal
            _buildCustomWithdrawal(),
          ],
        ),
      ),
    );
  }

  Widget _buildHistoryTab() {
    return Column(
      children: [
        if (_payoutHistory.any((p) => p.status == PayoutStatus.processing))
          Container(
            margin: const EdgeInsets.all(16),
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.orange.shade50,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.orange.shade200),
            ),
            child: Row(
              children: [
                Icon(Icons.hourglass_empty, color: Colors.orange.shade700),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Pending Withdrawals',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.orange.shade700,
                        ),
                      ),
                      Text(
                        'You have pending withdrawals totaling \$${_pendingAmount.toStringAsFixed(2)}',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.orange.shade600,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        Expanded(
          child: ListView.builder(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            itemCount: _payoutHistory.length,
            itemBuilder: (context, index) {
              final payout = _payoutHistory[index];
              return _buildPayoutHistoryCard(payout);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildBalanceCard() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: [Color(0xFF1B365D), Color(0xFF2A4A6B)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: const Color(0xFF1B365D).withValues(alpha: 0.3),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Available for Withdrawal',
            style: TextStyle(
              fontSize: 16,
              color: Colors.white70,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            '\$${_availableBalance.toStringAsFixed(2)}',
            style: const TextStyle(
              fontSize: 36,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          if (_pendingAmount > 0) ...[
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.2),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                'Pending: \$${_pendingAmount.toStringAsFixed(2)}',
                style: const TextStyle(
                  fontSize: 14,
                  color: Colors.white,
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildQuickWithdrawActions() {
    final quickAmounts = [50.0, 100.0, 200.0, 500.0];
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Quick Withdraw',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Color(0xFF1B365D),
          ),
        ),
        const SizedBox(height: 16),
        Wrap(
          spacing: 12,
          runSpacing: 12,
          children: quickAmounts.map((amount) {
            final canWithdraw = amount <= _availableBalance;
            return InkWell(
              onTap: canWithdraw ? () => _showWithdrawDialog(amount) : null,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
                decoration: BoxDecoration(
                  color: canWithdraw ? const Color(0xFF1B365D) : Colors.grey.shade300,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  '\$${amount.toStringAsFixed(0)}',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: canWithdraw ? Colors.white : Colors.grey.shade600,
                  ),
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildWithdrawalMethods() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            const Text(
              'Withdrawal Methods',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Color(0xFF1B365D),
              ),
            ),
            const Spacer(),
            TextButton.icon(
              onPressed: _showAddMethodDialog,
              icon: const Icon(Icons.add),
              label: const Text('Add Method'),
            ),
          ],
        ),
        const SizedBox(height: 16),
        ..._withdrawalMethods.map((method) => _buildMethodCard(method)),
      ],
    );
  }

  Widget _buildMethodCard(WithdrawalMethod method) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: method.isDefault ? const Color(0xFF1B365D) : Colors.grey.shade300,
          width: method.isDefault ? 2 : 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: const Color(0xFF1B365D).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  method.icon,
                  color: const Color(0xFF1B365D),
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Text(
                          method.type,
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: Color(0xFF1B365D),
                          ),
                        ),
                        if (method.isDefault) ...[
                          const SizedBox(width: 8),
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                            decoration: BoxDecoration(
                              color: Colors.green,
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: const Text(
                              'Default',
                              style: TextStyle(
                                fontSize: 10,
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ],
                      ],
                    ),
                    const SizedBox(height: 4),
                    Text(
                      method.name,
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey.shade600,
                      ),
                    ),
                  ],
                ),
              ),
              PopupMenuButton<String>(
                onSelected: (value) => _handleMethodAction(value, method),
                itemBuilder: (context) => [
                  if (!method.isDefault)
                    const PopupMenuItem(
                      value: 'set_default',
                      child: Text('Set as Default'),
                    ),
                  const PopupMenuItem(
                    value: 'edit',
                    child: Text('Edit'),
                  ),
                  const PopupMenuItem(
                    value: 'delete',
                    child: Text('Delete'),
                  ),
                ],
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: _buildMethodDetail('Processing', method.processingTime),
              ),
              Expanded(
                child: _buildMethodDetail(
                  'Fee',
                  method.fee > 0 ? '\$${method.fee.toStringAsFixed(2)}' : 'Free',
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildMethodDetail(String label, String value) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey.shade600,
          ),
        ),
        const SizedBox(height: 2),
        Text(
          value,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
          ),
        ),
      ],
    );
  }

  Widget _buildCustomWithdrawal() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Custom Amount',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Color(0xFF1B365D),
            ),
          ),
          const SizedBox(height: 16),
          ElevatedButton.icon(
            onPressed: () => _showWithdrawDialog(),
            icon: const Icon(Icons.account_balance_wallet),
            label: const Text('Withdraw Custom Amount'),
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF1B365D),
              foregroundColor: Colors.white,
              minimumSize: const Size(double.infinity, 48),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPayoutHistoryCard(PayoutRecord payout) {
    Color statusColor;
    IconData statusIcon;
    
    switch (payout.status) {
      case PayoutStatus.completed:
        statusColor = Colors.green;
        statusIcon = Icons.check_circle;
        break;
      case PayoutStatus.processing:
        statusColor = Colors.orange;
        statusIcon = Icons.hourglass_empty;
        break;
      case PayoutStatus.failed:
        statusColor = Colors.red;
        statusIcon = Icons.error;
        break;
    }

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(statusIcon, color: statusColor, size: 20),
              const SizedBox(width: 8),
              Text(
                '\$${payout.amount.toStringAsFixed(2)}',
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF1B365D),
                ),
              ),
              const Spacer(),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: statusColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  payout.status.toString().split('.').last.toUpperCase(),
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                    color: statusColor,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Method',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey.shade600,
                      ),
                    ),
                    Text(
                      payout.method,
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              ),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Requested',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey.shade600,
                      ),
                    ),
                    Text(
                      _formatDate(payout.requestDate),
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          if (payout.fee > 0) ...[
            const SizedBox(height: 8),
            Text(
              'Fee: \$${payout.fee.toStringAsFixed(2)}',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey.shade600,
              ),
            ),
          ],
          if (payout.failureReason != null) ...[
            const SizedBox(height: 8),
            Text(
              'Reason: ${payout.failureReason}',
              style: const TextStyle(
                fontSize: 12,
                color: Colors.red,
              ),
            ),
          ],
          const SizedBox(height: 8),
          Text(
            'ID: ${payout.transactionId}',
            style: TextStyle(
              fontSize: 10,
              color: Colors.grey.shade500,
              fontFamily: 'monospace',
            ),
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date).inDays;
    
    if (difference == 0) {
      return 'Today';
    } else if (difference == 1) {
      return 'Yesterday';
    } else if (difference < 7) {
      return '$difference days ago';
    } else {
      return '${date.day}/${date.month}/${date.year}';
    }
  }

  void _showWithdrawDialog([double? presetAmount]) {
    showDialog(
      context: context,
      builder: (context) => _WithdrawDialog(
        availableBalance: _availableBalance,
        presetAmount: presetAmount,
        withdrawalMethods: _withdrawalMethods,
        defaultMethod: _defaultMethod,
        onWithdraw: (amount, methodId) async {
          try {
            // In real app, call the API
            await Future.delayed(const Duration(seconds: 1));
            
            Navigator.of(context).pop();
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('Withdrawal request of \$${amount.toStringAsFixed(2)} submitted'),
                backgroundColor: Colors.green,
              ),
            );
            
            // Refresh data
            _loadPayoutData();
          } catch (e) {
            Navigator.of(context).pop();
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('Failed to submit withdrawal: $e'),
                backgroundColor: Colors.red,
              ),
            );
          }
        },
      ),
    );
  }

  void _showAddMethodDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Add Withdrawal Method'),
        content: const Text('This feature will be available soon. You can add bank accounts, PayPal, and other payment methods.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void _handleMethodAction(String action, WithdrawalMethod method) {
    switch (action) {
      case 'set_default':
        setState(() {
          for (var m in _withdrawalMethods) {
            m.isDefault = m.id == method.id;
          }
          _defaultMethod = method.id;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('${method.type} set as default')),
        );
        break;
      case 'edit':
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Edit method feature coming soon')),
        );
        break;
      case 'delete':
        if (method.isDefault) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Cannot delete default method. Set another as default first.'),
              backgroundColor: Colors.red,
            ),
          );
        } else {
          _showDeleteConfirmation(method);
        }
        break;
    }
  }

  void _showDeleteConfirmation(WithdrawalMethod method) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Method'),
        content: Text('Are you sure you want to delete ${method.type}?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              setState(() {
                _withdrawalMethods.removeWhere((m) => m.id == method.id);
              });
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text('${method.type} deleted')),
              );
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }
}

class _WithdrawDialog extends StatefulWidget {
  final double availableBalance;
  final double? presetAmount;
  final List<WithdrawalMethod> withdrawalMethods;
  final String? defaultMethod;
  final Function(double amount, String methodId) onWithdraw;

  const _WithdrawDialog({
    required this.availableBalance,
    this.presetAmount,
    required this.withdrawalMethods,
    this.defaultMethod,
    required this.onWithdraw,
  });

  @override
  State<_WithdrawDialog> createState() => _WithdrawDialogState();
}

class _WithdrawDialogState extends State<_WithdrawDialog> {
  final _formKey = GlobalKey<FormState>();
  final _amountController = TextEditingController();
  String? _selectedMethod;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    if (widget.presetAmount != null) {
      _amountController.text = widget.presetAmount!.toStringAsFixed(2);
    }
    _selectedMethod = widget.defaultMethod;
  }

  @override
  void dispose() {
    _amountController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final selectedMethodObj = widget.withdrawalMethods
        .firstWhere((m) => m.id == _selectedMethod, orElse: () => widget.withdrawalMethods.first);
    
    return AlertDialog(
      title: const Text('Withdraw Funds'),
      content: Form(
        key: _formKey,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Available: \$${widget.availableBalance.toStringAsFixed(2)}',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey.shade600,
              ),
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _amountController,
              keyboardType: TextInputType.number,
              inputFormatters: [FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,2}'))],
              decoration: const InputDecoration(
                labelText: 'Amount',
                prefixText: '\$',
                border: OutlineInputBorder(),
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter an amount';
                }
                final amount = double.tryParse(value);
                if (amount == null || amount <= 0) {
                  return 'Please enter a valid amount';
                }
                if (amount > widget.availableBalance) {
                  return 'Amount exceeds available balance';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            DropdownButtonFormField<String>(
              value: _selectedMethod,
              decoration: const InputDecoration(
                labelText: 'Withdrawal Method',
                border: OutlineInputBorder(),
              ),
              items: widget.withdrawalMethods.map((method) {
                return DropdownMenuItem(
                  value: method.id,
                  child: Row(
                    children: [
                      Icon(method.icon, size: 20),
                      const SizedBox(width: 8),
                      Text(method.type),
                      if (method.isDefault) ...[
                        const SizedBox(width: 8),
                        const Text('(Default)', style: TextStyle(fontSize: 12)),
                      ],
                    ],
                  ),
                );
              }).toList(),
              onChanged: (value) {
                setState(() {
                  _selectedMethod = value;
                });
              },
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.blue.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.blue.shade200),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Processing Time: ${selectedMethodObj.processingTime}',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.blue.shade700,
                    ),
                  ),
                  if (selectedMethodObj.fee > 0)
                    Text(
                      'Fee: \$${selectedMethodObj.fee.toStringAsFixed(2)}',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.blue.shade700,
                      ),
                    ),
                ],
              ),
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: _isLoading ? null : _handleWithdraw,
          child: _isLoading
              ? const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : const Text('Withdraw'),
        ),
      ],
    );
  }

  void _handleWithdraw() async {
    if (!_formKey.currentState!.validate() || _selectedMethod == null) return;

    setState(() {
      _isLoading = true;
    });

    final amount = double.parse(_amountController.text);
    await widget.onWithdraw(amount, _selectedMethod!);

    setState(() {
      _isLoading = false;
    });
  }
}

// Data Models for Payouts
class PayoutRecord {
  final int id;
  final double amount;
  final String method;
  final PayoutStatus status;
  final DateTime requestDate;
  final DateTime? completedDate;
  final double fee;
  final String transactionId;
  final String? failureReason;

  PayoutRecord({
    required this.id,
    required this.amount,
    required this.method,
    required this.status,
    required this.requestDate,
    this.completedDate,
    required this.fee,
    required this.transactionId,
    this.failureReason,
  });
}

class WithdrawalMethod {
  final String id;
  final String type;
  final String name;
  final IconData icon;
  bool isDefault;
  final String processingTime;
  final double fee;

  WithdrawalMethod({
    required this.id,
    required this.type,
    required this.name,
    required this.icon,
    required this.isDefault,
    required this.processingTime,
    required this.fee,
  });
}

enum PayoutStatus {
  processing,
  completed,
  failed,
}