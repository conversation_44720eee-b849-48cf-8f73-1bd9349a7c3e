import 'package:flutter/material.dart';
import 'package:file_picker/file_picker.dart';

class DocumentsScreen extends StatefulWidget {
  const DocumentsScreen({super.key});

  @override
  State<DocumentsScreen> createState() => _DocumentsScreenState();
}

class _DocumentsScreenState extends State<DocumentsScreen> {
  bool _isLoading = true;
  List<DocumentItem> _documents = [];
  List<DocumentType> _requiredDocuments = [];
  bool _isVerificationComplete = false;

  @override
  void initState() {
    super.initState();
    _loadDocuments();
  }

  Future<void> _loadDocuments() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // In a real app, this would be an API call
      final documents = await _getMockDocuments();
      final requiredDocs = await _getRequiredDocuments();
      
      setState(() {
        _documents = documents;
        _requiredDocuments = requiredDocs;
        _isVerificationComplete = _checkVerificationStatus();
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to load documents: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  Future<List<DocumentItem>> _getMockDocuments() async {
    // Simulate API delay
    await Future.delayed(const Duration(milliseconds: 800));
    
    return [
      DocumentItem(
        id: '1',
        type: DocumentTypeEnum.driversLicense,
        name: 'Driver\'s License',
        fileName: 'drivers_license.pdf',
        uploadDate: DateTime.now().subtract(const Duration(days: 15)),
        status: DocumentStatus.approved,
        expiryDate: DateTime.now().add(const Duration(days: 365)),
        url: 'https://example.com/docs/drivers_license.pdf',
        notes: 'Valid until 2025',
      ),
      DocumentItem(
        id: '2',
        type: DocumentTypeEnum.socialSecurity,
        name: 'Social Security Card',
        fileName: 'social_security.jpg',
        uploadDate: DateTime.now().subtract(const Duration(days: 20)),
        status: DocumentStatus.approved,
        url: 'https://example.com/docs/social_security.jpg',
      ),
      DocumentItem(
        id: '3',
        type: DocumentTypeEnum.backgroundCheck,
        name: 'Background Check',
        fileName: 'background_check.pdf',
        uploadDate: DateTime.now().subtract(const Duration(days: 10)),
        status: DocumentStatus.pending,
        url: 'https://example.com/docs/background_check.pdf',
        notes: 'Under review - processing time 3-5 business days',
      ),
      DocumentItem(
        id: '4',
        type: DocumentTypeEnum.proofOfInsurance,
        name: 'Insurance Certificate',
        fileName: 'insurance_cert.pdf',
        uploadDate: DateTime.now().subtract(const Duration(days: 5)),
        status: DocumentStatus.rejected,
        url: 'https://example.com/docs/insurance_cert.pdf',
        notes: 'Document expired. Please upload current insurance certificate.',
        rejectionReason: 'Document shows expiry date of last month',
      ),
    ];
  }

  Future<List<DocumentType>> _getRequiredDocuments() async {
    return [
      DocumentType(
        type: DocumentTypeEnum.driversLicense,
        name: 'Driver\'s License',
        description: 'Valid government-issued driver\'s license',
        isRequired: true,
        acceptedFormats: ['PDF', 'JPG', 'PNG'],
        maxSizeMB: 5,
      ),
      DocumentType(
        type: DocumentTypeEnum.socialSecurity,
        name: 'Social Security Card',
        description: 'Social Security card or equivalent ID document',
        isRequired: true,
        acceptedFormats: ['PDF', 'JPG', 'PNG'],
        maxSizeMB: 5,
      ),
      DocumentType(
        type: DocumentTypeEnum.backgroundCheck,
        name: 'Background Check',
        description: 'Criminal background check (we can help you obtain this)',
        isRequired: true,
        acceptedFormats: ['PDF'],
        maxSizeMB: 10,
      ),
      DocumentType(
        type: DocumentTypeEnum.proofOfInsurance,
        name: 'Proof of Insurance',
        description: 'Current liability insurance certificate',
        isRequired: true,
        acceptedFormats: ['PDF', 'JPG', 'PNG'],
        maxSizeMB: 5,
      ),
      DocumentType(
        type: DocumentTypeEnum.bankStatement,
        name: 'Bank Statement',
        description: 'Recent bank statement for direct deposit setup',
        isRequired: false,
        acceptedFormats: ['PDF'],
        maxSizeMB: 5,
      ),
      DocumentType(
        type: DocumentTypeEnum.w9Form,
        name: 'W-9 Tax Form',
        description: 'Completed W-9 form for tax reporting',
        isRequired: false,
        acceptedFormats: ['PDF'],
        maxSizeMB: 3,
      ),
    ];
  }

  bool _checkVerificationStatus() {
    final requiredDocs = _requiredDocuments.where((doc) => doc.isRequired).toList();
    final approvedRequiredDocs = _documents
        .where((doc) => doc.status == DocumentStatus.approved)
        .where((doc) => _requiredDocuments
            .where((req) => req.isRequired)
            .any((req) => req.type == doc.type))
        .length;
    
    return approvedRequiredDocs == requiredDocs.length;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      appBar: AppBar(
        title: const Text('Documents'),
        backgroundColor: const Color(0xFF1B365D),
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadDocuments,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Verification Status
                  _buildVerificationStatus(),
                  
                  // Required Documents Section
                  _buildRequiredDocuments(),
                  
                  // Optional Documents Section
                  _buildOptionalDocuments(),
                  
                  // Help Section
                  _buildHelpSection(),
                ],
              ),
            ),
    );
  }

  Widget _buildVerificationStatus() {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: _isVerificationComplete ? Colors.green.shade50 : Colors.orange.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: _isVerificationComplete ? Colors.green.shade200 : Colors.orange.shade200,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                _isVerificationComplete ? Icons.verified_user : Icons.pending,
                color: _isVerificationComplete ? Colors.green.shade700 : Colors.orange.shade700,
                size: 28,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      _isVerificationComplete ? 'Verification Complete' : 'Verification Pending',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: _isVerificationComplete ? Colors.green.shade700 : Colors.orange.shade700,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      _isVerificationComplete
                          ? 'All required documents have been approved. You can now accept jobs!'
                          : 'Please complete document verification to start accepting jobs.',
                      style: TextStyle(
                        fontSize: 14,
                        color: _isVerificationComplete ? Colors.green.shade600 : Colors.orange.shade600,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          if (!_isVerificationComplete) ...[
            const SizedBox(height: 16),
            LinearProgressIndicator(
              value: _getVerificationProgress(),
              backgroundColor: Colors.orange.shade100,
              valueColor: AlwaysStoppedAnimation<Color>(Colors.orange.shade400),
            ),
            const SizedBox(height: 8),
            Text(
              '${(_getVerificationProgress() * 100).round()}% Complete',
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w600,
                color: Colors.orange.shade600,
              ),
            ),
          ],
        ],
      ),
    );
  }

  double _getVerificationProgress() {
    final requiredDocs = _requiredDocuments.where((doc) => doc.isRequired).length;
    final approvedDocs = _documents
        .where((doc) => doc.status == DocumentStatus.approved)
        .where((doc) => _requiredDocuments
            .where((req) => req.isRequired)
            .any((req) => req.type == doc.type))
        .length;
    
    return requiredDocs > 0 ? approvedDocs / requiredDocs : 0.0;
  }

  Widget _buildRequiredDocuments() {
    final requiredDocs = _requiredDocuments.where((doc) => doc.isRequired).toList();
    
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(20),
            child: Row(
              children: [
                const Icon(Icons.assignment_turned_in, color: Color(0xFF1B365D)),
                const SizedBox(width: 12),
                const Text(
                  'Required Documents',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF1B365D),
                  ),
                ),
                const Spacer(),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: const Color(0xFF1B365D).withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    '${requiredDocs.length} required',
                    style: const TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                      color: Color(0xFF1B365D),
                    ),
                  ),
                ),
              ],
            ),
          ),
          ...requiredDocs.map((docType) => _buildDocumentItem(docType)),
        ],
      ),
    );
  }

  Widget _buildOptionalDocuments() {
    final optionalDocs = _requiredDocuments.where((doc) => !doc.isRequired).toList();
    
    return Container(
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(20),
            child: Row(
              children: [
                const Icon(Icons.description, color: Colors.grey),
                const SizedBox(width: 12),
                const Text(
                  'Optional Documents',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF1B365D),
                  ),
                ),
                const Spacer(),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.grey.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    'Optional',
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                      color: Colors.grey.shade600,
                    ),
                  ),
                ),
              ],
            ),
          ),
          ...optionalDocs.map((docType) => _buildDocumentItem(docType)),
        ],
      ),
    );
  }

  Widget _buildDocumentItem(DocumentType docType) {
    final existingDoc = _documents.firstWhere(
      (doc) => doc.type == docType.type,
      orElse: () => DocumentItem(
        id: '',
        type: docType.type,
        name: docType.name,
        fileName: '',
        uploadDate: DateTime.now(),
        status: DocumentStatus.notUploaded,
      ),
    );

    Color statusColor;
    IconData statusIcon;
    String statusText;

    switch (existingDoc.status) {
      case DocumentStatus.approved:
        statusColor = Colors.green;
        statusIcon = Icons.check_circle;
        statusText = 'Approved';
        break;
      case DocumentStatus.pending:
        statusColor = Colors.orange;
        statusIcon = Icons.hourglass_empty;
        statusText = 'Under Review';
        break;
      case DocumentStatus.rejected:
        statusColor = Colors.red;
        statusIcon = Icons.error;
        statusText = 'Rejected';
        break;
      case DocumentStatus.notUploaded:
        statusColor = Colors.grey;
        statusIcon = Icons.upload_file;
        statusText = docType.isRequired ? 'Required' : 'Optional';
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(color: Colors.grey.shade200),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: statusColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  statusIcon,
                  color: statusColor,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      docType.name,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF1B365D),
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      docType.description,
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey.shade600,
                      ),
                    ),
                    if (existingDoc.status != DocumentStatus.notUploaded) ...[
                      const SizedBox(height: 4),
                      Text(
                        'Uploaded ${_formatDate(existingDoc.uploadDate)}',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey.shade500,
                        ),
                      ),
                    ],
                  ],
                ),
              ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: statusColor.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      statusText,
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                        color: statusColor,
                      ),
                    ),
                  ),
                  const SizedBox(height: 8),
                  _buildActionButton(docType, existingDoc),
                ],
              ),
            ],
          ),
          if (existingDoc.status == DocumentStatus.rejected && existingDoc.rejectionReason != null) ...[
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.red.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.red.shade200),
              ),
              child: Row(
                children: [
                  Icon(Icons.info_outline, color: Colors.red.shade700, size: 16),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      existingDoc.rejectionReason!,
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.red.shade700,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
          if (existingDoc.notes != null) ...[
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.blue.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.blue.shade200),
              ),
              child: Row(
                children: [
                  Icon(Icons.info_outline, color: Colors.blue.shade700, size: 16),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      existingDoc.notes!,
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.blue.shade700,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildActionButton(DocumentType docType, DocumentItem existingDoc) {
    switch (existingDoc.status) {
      case DocumentStatus.notUploaded:
        return ElevatedButton.icon(
          onPressed: () => _uploadDocument(docType),
          icon: const Icon(Icons.upload, size: 16),
          label: const Text('Upload'),
          style: ElevatedButton.styleFrom(
            backgroundColor: const Color(0xFF1B365D),
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            minimumSize: Size.zero,
          ),
        );
      case DocumentStatus.approved:
        return Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            IconButton(
              onPressed: () => _viewDocument(existingDoc),
              icon: const Icon(Icons.visibility, size: 20),
              style: IconButton.styleFrom(
                padding: const EdgeInsets.all(8),
                minimumSize: Size.zero,
              ),
            ),
            IconButton(
              onPressed: () => _replaceDocument(docType, existingDoc),
              icon: const Icon(Icons.refresh, size: 20),
              style: IconButton.styleFrom(
                padding: const EdgeInsets.all(8),
                minimumSize: Size.zero,
              ),
            ),
          ],
        );
      case DocumentStatus.pending:
        return Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            IconButton(
              onPressed: () => _viewDocument(existingDoc),
              icon: const Icon(Icons.visibility, size: 20),
              style: IconButton.styleFrom(
                padding: const EdgeInsets.all(8),
                minimumSize: Size.zero,
              ),
            ),
          ],
        );
      case DocumentStatus.rejected:
        return ElevatedButton.icon(
          onPressed: () => _replaceDocument(docType, existingDoc),
          icon: const Icon(Icons.refresh, size: 16),
          label: const Text('Re-upload'),
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.orange,
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            minimumSize: Size.zero,
          ),
        );
    }
  }

  Widget _buildHelpSection() {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(Icons.help_outline, color: Color(0xFF1B365D)),
              const SizedBox(width: 12),
              const Text(
                'Need Help?',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF1B365D),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          _buildHelpItem(
            'Document Requirements',
            'Learn about accepted file formats and requirements',
            Icons.info,
            () => _showDocumentRequirements(),
          ),
          _buildHelpItem(
            'Background Check',
            'Get help obtaining your background check',
            Icons.security,
            () => _showBackgroundCheckHelp(),
          ),
          _buildHelpItem(
            'Contact Support',
            'Get help from our verification team',
            Icons.support_agent,
            () => _contactSupport(),
          ),
        ],
      ),
    );
  }

  Widget _buildHelpItem(String title, String subtitle, IconData icon, VoidCallback onTap) {
    return InkWell(
      onTap: onTap,
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 8),
        child: Row(
          children: [
            Icon(icon, color: Colors.grey.shade600, size: 20),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  Text(
                    subtitle,
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey.shade600,
                    ),
                  ),
                ],
              ),
            ),
            Icon(Icons.arrow_forward_ios, color: Colors.grey.shade400, size: 16),
          ],
        ),
      ),
    );
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date).inDays;
    
    if (difference == 0) {
      return 'today';
    } else if (difference == 1) {
      return 'yesterday';
    } else if (difference < 7) {
      return '$difference days ago';
    } else {
      return '${date.day}/${date.month}/${date.year}';
    }
  }

  Future<void> _uploadDocument(DocumentType docType) async {
    try {
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: docType.acceptedFormats.map((f) => f.toLowerCase()).toList(),
        allowMultiple: false,
      );

      if (result != null) {
        PlatformFile file = result.files.first;
        
        // Check file size
        if (file.size > docType.maxSizeMB * 1024 * 1024) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('File size must be less than ${docType.maxSizeMB}MB'),
              backgroundColor: Colors.red,
            ),
          );
          return;
        }

        _showUploadProgress(docType, file);
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to pick file: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _showUploadProgress(DocumentType docType, PlatformFile file) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => _UploadProgressDialog(
        docType: docType,
        file: file,
        onUploadComplete: (success, message) {
          Navigator.of(context).pop();
          if (success) {
            _loadDocuments();
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(message ?? 'Document uploaded successfully'),
                backgroundColor: Colors.green,
              ),
            );
          } else {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(message ?? 'Failed to upload document'),
                backgroundColor: Colors.red,
              ),
            );
          }
        },
      ),
    );
  }

  void _viewDocument(DocumentItem document) {
    // In a real app, this would open the document viewer
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Opening ${document.fileName}')),
    );
  }

  void _replaceDocument(DocumentType docType, DocumentItem existingDoc) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Replace Document'),
        content: Text('Are you sure you want to replace ${docType.name}?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _uploadDocument(docType);
            },
            child: const Text('Replace'),
          ),
        ],
      ),
    );
  }

  void _showDocumentRequirements() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.7,
        maxChildSize: 0.9,
        minChildSize: 0.5,
        builder: (context, scrollController) {
          return Container(
            decoration: const BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
            ),
            child: Column(
              children: [
                Container(
                  margin: const EdgeInsets.only(top: 8),
                  width: 40,
                  height: 4,
                  decoration: BoxDecoration(
                    color: Colors.grey.shade300,
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
                const Padding(
                  padding: EdgeInsets.all(20),
                  child: Text(
                    'Document Requirements',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: Color(0xFF1B365D),
                    ),
                  ),
                ),
                Expanded(
                  child: SingleChildScrollView(
                    controller: scrollController,
                    padding: const EdgeInsets.symmetric(horizontal: 20),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'File Requirements:',
                          style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                        ),
                        const SizedBox(height: 8),
                        const Text('• Accepted formats: PDF, JPG, PNG'),
                        const Text('• Maximum file size: 5MB (10MB for background check)'),
                        const Text('• Images should be clear and readable'),
                        const Text('• No blurry or dark photos'),
                        const SizedBox(height: 16),
                        const Text(
                          'Document Quality:',
                          style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                        ),
                        const SizedBox(height: 8),
                        const Text('• All text must be clearly visible'),
                        const Text('• No damaged or altered documents'),
                        const Text('• Documents must be current and valid'),
                        const Text('• Full document must be visible (no cropping)'),
                        const SizedBox(height: 40),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  void _showBackgroundCheckHelp() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Background Check Help'),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('We can help you obtain your background check:'),
            SizedBox(height: 12),
            Text('• Fast online processing'),
            Text('• Covers all required checks'),
            Text('• Results typically ready in 2-3 days'),
            SizedBox(height: 12),
            Text('Cost: \$25 (one-time fee)'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              // In real app, navigate to background check service
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Opening background check service...')),
              );
            },
            child: const Text('Get Background Check'),
          ),
        ],
      ),
    );
  }

  void _contactSupport() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Contact Support'),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Need help with document verification?'),
            SizedBox(height: 12),
            Text('📧 Email: <EMAIL>'),
            Text('📞 Phone: 1-800-RABBIT-1'),
            Text('💬 Live Chat: Available 9 AM - 6 PM EST'),
            SizedBox(height: 12),
            Text('Average response time: 2-4 hours'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Opening support chat...')),
              );
            },
            child: const Text('Start Chat'),
          ),
        ],
      ),
    );
  }
}

class _UploadProgressDialog extends StatefulWidget {
  final DocumentType docType;
  final PlatformFile file;
  final Function(bool success, String? message) onUploadComplete;

  const _UploadProgressDialog({
    required this.docType,
    required this.file,
    required this.onUploadComplete,
  });

  @override
  State<_UploadProgressDialog> createState() => _UploadProgressDialogState();
}

class _UploadProgressDialogState extends State<_UploadProgressDialog> {
  double _progress = 0.0;
  final bool _isUploading = true;

  @override
  void initState() {
    super.initState();
    _simulateUpload();
  }

  void _simulateUpload() async {
    // Simulate upload progress
    for (int i = 0; i <= 100; i += 10) {
      await Future.delayed(const Duration(milliseconds: 200));
      if (mounted) {
        setState(() {
          _progress = i / 100;
        });
      }
    }

    // Simulate upload completion
    await Future.delayed(const Duration(milliseconds: 500));
    
    // In real app, call the actual upload API
    try {
      // final result = await RunnerService.uploadDocument(File(widget.file.path!));
      widget.onUploadComplete(true, 'Document uploaded successfully and is under review');
    } catch (e) {
      widget.onUploadComplete(false, 'Failed to upload document: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Uploading Document'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text('Uploading ${widget.file.name}...'),
          const SizedBox(height: 20),
          LinearProgressIndicator(value: _progress),
          const SizedBox(height: 10),
          Text('${(_progress * 100).round()}%'),
        ],
      ),
    );
  }
}

// Data Models
class DocumentItem {
  final String id;
  final DocumentTypeEnum type;
  final String name;
  final String fileName;
  final DateTime uploadDate;
  final DocumentStatus status;
  final DateTime? expiryDate;
  final String? url;
  final String? notes;
  final String? rejectionReason;

  DocumentItem({
    required this.id,
    required this.type,
    required this.name,
    required this.fileName,
    required this.uploadDate,
    required this.status,
    this.expiryDate,
    this.url,
    this.notes,
    this.rejectionReason,
  });
}

class DocumentType {
  final DocumentTypeEnum type;
  final String name;
  final String description;
  final bool isRequired;
  final List<String> acceptedFormats;
  final int maxSizeMB;

  DocumentType({
    required this.type,
    required this.name,
    required this.description,
    required this.isRequired,
    required this.acceptedFormats,
    required this.maxSizeMB,
  });
}

enum DocumentTypeEnum {
  driversLicense,
  socialSecurity,
  backgroundCheck,
  proofOfInsurance,
  bankStatement,
  w9Form,
}

enum DocumentStatus {
  notUploaded,
  pending,
  approved,
  rejected,
}