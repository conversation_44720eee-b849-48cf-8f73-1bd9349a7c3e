class Service {
  final String id;
  final String name;
  final String slug;
  final String description;
  final String category;
  final double basePrice;
  final String priceUnit;
  final String? iconUrl;
  final bool isActive;
  final bool requiresVehicle;
  final int? estimatedDuration; // in minutes
  final DateTime createdAt;
  final DateTime updatedAt;

  Service({
    required this.id,
    required this.name,
    required this.slug,
    required this.description,
    required this.category,
    required this.basePrice,
    required this.priceUnit,
    this.iconUrl,
    required this.isActive,
    required this.requiresVehicle,
    this.estimatedDuration,
    required this.createdAt,
    required this.updatedAt,
  });

  // Backward compatibility getters
  double get price => basePrice;
  String? get imageUrl => iconUrl;

  factory Service.fromJson(Map<String, dynamic> json) {
    return Service(
      id: json['id'],
      name: json['name'],
      slug: json['slug'],
      description: json['description'],
      category: json['category'],
      basePrice: double.tryParse(json['base_price'].toString()) ?? 0.0,
      priceUnit: json['price_unit'] ?? 'per_hour',
      iconUrl: json['icon_url'],
      isActive: json['is_active'] ?? true,
      requiresVehicle: json['requires_vehicle'] ?? false,
      estimatedDuration: json['estimated_duration'],
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: DateTime.parse(json['updated_at']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'slug': slug,
      'description': description,
      'category': category,
      'base_price': basePrice,
      'price_unit': priceUnit,
      'icon_url': iconUrl,
      'is_active': isActive,
      'requires_vehicle': requiresVehicle,
      'estimated_duration': estimatedDuration,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  String get formattedPrice => '\$${basePrice.toStringAsFixed(2)}${priceUnit == "per_hour" ? "/hr" : priceUnit == "per_task" ? "/task" : ""}';
  
  String get formattedDuration {
    if (estimatedDuration == null) return 'Time varies';
    final duration = estimatedDuration!;
    if (duration < 60) {
      return '$duration min';
    } else {
      final hours = duration ~/ 60;
      final minutes = duration % 60;
      if (minutes == 0) {
        return '$hours hr${hours > 1 ? 's' : ''}';
      } else {
        return '$hours hr $minutes min';
      }
    }
  }
}

class Booking {
  final String id;
  final String bookingNumber;
  final String clientId;
  final String? agentId;
  final String? serviceId;
  final String? packageId;
  final BookingStatus status;
  final DateTime scheduledDate;
  final String scheduledTime;
  final String serviceLocation;
  final Map<String, dynamic>? locationCoordinates;
  final String? specialInstructions;
  final int? estimatedDuration;
  final int? actualDuration;
  final double basePrice;
  final double distanceCharge;
  final double timeCharge;
  final double totalPrice;
  final String currency;
  final String? cancellationReason;
  final String? cancelledBy;
  final DateTime? cancelledAt;
  final DateTime? startedAt;
  final DateTime? completedAt;
  final DateTime createdAt;
  final DateTime updatedAt;

  Booking({
    required this.id,
    required this.bookingNumber,
    required this.clientId,
    this.agentId,
    this.serviceId,
    this.packageId,
    required this.status,
    required this.scheduledDate,
    required this.scheduledTime,
    required this.serviceLocation,
    this.locationCoordinates,
    this.specialInstructions,
    this.estimatedDuration,
    this.actualDuration,
    required this.basePrice,
    required this.distanceCharge,
    required this.timeCharge,
    required this.totalPrice,
    required this.currency,
    this.cancellationReason,
    this.cancelledBy,
    this.cancelledAt,
    this.startedAt,
    this.completedAt,
    required this.createdAt,
    required this.updatedAt,
  });

  // Backward compatibility getters
  String get serviceName => 'Service Booking'; // Default name, could be enhanced with service lookup
  String get location => serviceLocation;
  double get totalAmount => totalPrice;
  String? get runnerName => agentId != null ? 'Agent $agentId' : null; // Could be enhanced with agent lookup
  String? get runnerPhone => null; // Would need agent data
  String get notes => specialInstructions ?? '';

  factory Booking.fromJson(Map<String, dynamic> json) {
    return Booking(
      id: json['id'],
      bookingNumber: json['booking_number'],
      clientId: json['client_id'],
      agentId: json['agent_id'],
      serviceId: json['service_id'],
      packageId: json['package_id'],
      status: BookingStatus.values.firstWhere(
        (status) => status.toString().split('.').last == json['status'],
        orElse: () => BookingStatus.pending,
      ),
      scheduledDate: DateTime.parse(json['scheduled_date']),
      scheduledTime: json['scheduled_time'],
      serviceLocation: json['service_location'],
      locationCoordinates: json['location_coordinates'] != null 
          ? Map<String, dynamic>.from(json['location_coordinates'])
          : null,
      specialInstructions: json['special_instructions'],
      estimatedDuration: json['estimated_duration'],
      actualDuration: json['actual_duration'],
      basePrice: double.tryParse(json['base_price'].toString()) ?? 0.0,
      distanceCharge: double.tryParse(json['distance_charge'].toString()) ?? 0.0,
      timeCharge: double.tryParse(json['time_charge'].toString()) ?? 0.0,
      totalPrice: double.tryParse(json['total_price'].toString()) ?? 0.0,
      currency: json['currency'] ?? 'USD',
      cancellationReason: json['cancellation_reason'],
      cancelledBy: json['cancelled_by'],
      cancelledAt: json['cancelled_at'] != null 
          ? DateTime.parse(json['cancelled_at'])
          : null,
      startedAt: json['started_at'] != null 
          ? DateTime.parse(json['started_at'])
          : null,
      completedAt: json['completed_at'] != null 
          ? DateTime.parse(json['completed_at'])
          : null,
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: DateTime.parse(json['updated_at']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'booking_number': bookingNumber,
      'client_id': clientId,
      'agent_id': agentId,
      'service_id': serviceId,
      'package_id': packageId,
      'status': status.toString().split('.').last,
      'scheduled_date': scheduledDate.toIso8601String(),
      'scheduled_time': scheduledTime,
      'service_location': serviceLocation,
      'location_coordinates': locationCoordinates,
      'special_instructions': specialInstructions,
      'estimated_duration': estimatedDuration,
      'actual_duration': actualDuration,
      'base_price': basePrice,
      'distance_charge': distanceCharge,
      'time_charge': timeCharge,
      'total_price': totalPrice,
      'currency': currency,
      'cancellation_reason': cancellationReason,
      'cancelled_by': cancelledBy,
      'cancelled_at': cancelledAt?.toIso8601String(),
      'started_at': startedAt?.toIso8601String(),
      'completed_at': completedAt?.toIso8601String(),
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }
}

enum BookingStatus {
  pending,
  confirmed,
  in_progress,
  completed,
  cancelled,
}

class CreateBookingRequest {
  final String serviceId;
  final DateTime bookingDate;
  final String address;
  final Map<String, dynamic>? locationCoordinates;
  final String? specialInstructions;

  CreateBookingRequest({
    required this.serviceId,
    required this.bookingDate,
    required this.address,
    this.locationCoordinates,
    this.specialInstructions,
  });

  Map<String, dynamic> toJson() {
    return {
      'service_id': serviceId,
      'booking_date': bookingDate.toIso8601String(),
      'address': address,
      if (locationCoordinates != null) 'location_coordinates': locationCoordinates,
      if (specialInstructions != null) 'special_instructions': specialInstructions,
    };
  }
}