# 💳 Zimbabwe Payment Gateway Setup Guide

## 📋 Overview
This guide covers setting up EcoCash, Zipit, and card payment processing for the TaskRabitInc mobile app in Zimbabwe.

## 🇿🇼 Step 1: EcoCash Integration

### 1. EcoCash Merchant Account Setup
**Contact Econet Zimbabwe:**
- **Website**: https://www.econet.co.zw/
- **Email**: <EMAIL>
- **Phone**: +263 8677 000 000

**Required Documents:**
- Business registration certificate
- Tax clearance certificate
- Bank account details
- Director's ID copies
- Proof of address

### 2. EcoCash API Credentials
Once approved, you'll receive:
```env
ECOCASH_API_URL="https://ecocash-api.econet.co.zw"
ECOCASH_MERCHANT_CODE="EC123456"
ECOCASH_API_KEY="your-api-key-here"
ECOCASH_WEBHOOK_SECRET="your-webhook-secret"
```

### 3. EcoCash Integration Points
The mobile app supports these EcoCash features:
- **Payment Processing**: Direct mobile money payments
- **Status Checking**: Real-time payment verification
- **Webhook Handling**: Automatic payment confirmations
- **Refund Processing**: Automated refunds when needed

## 💰 Step 2: Zipit Integration

### 1. Zipit Merchant Account Setup
**Contact ZimSwitch:**
- **Website**: https://www.zimswitch.co.zw/
- **Email**: <EMAIL>
- **Phone**: +263 4 744 830

**Required Documents:**
- Business registration certificate
- Banking details
- Tax compliance certificate
- Director identification

### 2. Zipit API Credentials
Upon approval, configure:
```env
ZIPIT_API_URL="https://api.zipit.co.zw"
ZIPIT_MERCHANT_ID="ZP789012"
ZIPIT_API_SECRET="your-api-secret-here"
ZIPIT_WEBHOOK_URL="https://your-domain.com/api/v1/webhooks/zipit"
```

### 3. Zipit Features
The app integrates with:
- **Instant Payments**: Real-time Zipit transactions
- **Multiple Banks**: All Zipit-enabled banks supported
- **Transaction History**: Complete payment records
- **Dispute Handling**: Built-in dispute resolution

## 💳 Step 3: Card Payment Processing

### 1. Local Card Processor Options

#### Option A: ZimSwitch (Recommended)
**Contact**: <EMAIL>
**Features**:
- Local Zimbabwean cards
- Visa and Mastercard support
- Lower fees for local transactions
- Local currency support (ZWL)

#### Option B: Stripe (International)
**Website**: https://stripe.com/
**Features**:
- International card support
- USD processing
- Advanced fraud protection
- Comprehensive dashboard

### 2. Card Processing Configuration
```env
# ZimSwitch Configuration
ZIMSWITCH_API_URL="https://api.zimswitch.co.zw"
ZIMSWITCH_MERCHANT_ID="your-merchant-id"
ZIMSWITCH_SECRET_KEY="your-secret-key"

# Or Stripe Configuration (Alternative)
STRIPE_PUBLIC_KEY="pk_live_..."
STRIPE_SECRET_KEY="sk_live_..."
STRIPE_WEBHOOK_SECRET="whsec_..."
```

## 🏦 Step 4: Banking Integration

### 1. Local Banking Partners

#### CBZ Bank
- **API**: CBZ Connect
- **Contact**: <EMAIL>
- **Features**: Account verification, balance checks

#### Steward Bank
- **API**: Steward Bank API
- **Contact**: <EMAIL>
- **Features**: Mobile banking integration

### 2. Banking Configuration
```env
# CBZ Bank
CBZ_API_URL="https://api.cbz.co.zw"
CBZ_CLIENT_ID="your-client-id"
CBZ_CLIENT_SECRET="your-client-secret"

# Steward Bank  
STEWARD_API_URL="https://api.stewardbank.co.zw"
STEWARD_MERCHANT_CODE="your-merchant-code"
STEWARD_API_TOKEN="your-api-token"
```

## 🔧 Step 5: Backend Payment Configuration

### 1. Update Laravel Environment
Add to `.env` file:
```env
# Payment Gateway Settings
PAYMENT_CURRENCY="USD"
PAYMENT_DEFAULT_METHOD="ecocash"
PAYMENT_TIMEOUT_MINUTES=10

# EcoCash Settings
ECOCASH_ENABLED=true
ECOCASH_API_URL="https://ecocash-api.econet.co.zw"
ECOCASH_MERCHANT_CODE="your-merchant-code"
ECOCASH_API_KEY="your-api-key"
ECOCASH_WEBHOOK_SECRET="your-webhook-secret"

# Zipit Settings
ZIPIT_ENABLED=true
ZIPIT_API_URL="https://api.zipit.co.zw"
ZIPIT_MERCHANT_ID="your-merchant-id"
ZIPIT_API_SECRET="your-api-secret"
ZIPIT_WEBHOOK_SECRET="your-webhook-secret"

# Card Processing
CARD_PROCESSING_ENABLED=true
ZIMSWITCH_API_URL="https://api.zimswitch.co.zw"
ZIMSWITCH_MERCHANT_ID="your-merchant-id"
ZIMSWITCH_SECRET_KEY="your-secret-key"

# Banking Integration
BANKING_ENABLED=true
CBZ_API_URL="https://api.cbz.co.zw"
CBZ_CLIENT_ID="your-client-id"
CBZ_CLIENT_SECRET="your-client-secret"
```

### 2. Payment Gateway Configuration
Create `config/payments.php`:
```php
<?php

return [
    'default_currency' => env('PAYMENT_CURRENCY', 'USD'),
    'timeout_minutes' => env('PAYMENT_TIMEOUT_MINUTES', 10),
    
    'ecocash' => [
        'enabled' => env('ECOCASH_ENABLED', false),
        'api_url' => env('ECOCASH_API_URL'),
        'merchant_code' => env('ECOCASH_MERCHANT_CODE'),
        'api_key' => env('ECOCASH_API_KEY'),
        'webhook_secret' => env('ECOCASH_WEBHOOK_SECRET'),
    ],
    
    'zipit' => [
        'enabled' => env('ZIPIT_ENABLED', false),
        'api_url' => env('ZIPIT_API_URL'),
        'merchant_id' => env('ZIPIT_MERCHANT_ID'),
        'api_secret' => env('ZIPIT_API_SECRET'),
        'webhook_secret' => env('ZIPIT_WEBHOOK_SECRET'),
    ],
    
    'zimswitch' => [
        'enabled' => env('CARD_PROCESSING_ENABLED', false),
        'api_url' => env('ZIMSWITCH_API_URL'),
        'merchant_id' => env('ZIMSWITCH_MERCHANT_ID'),
        'secret_key' => env('ZIMSWITCH_SECRET_KEY'),
    ],
];
```

## 🧪 Step 6: Testing Payment Integration

### 1. Test Payment Methods Endpoint
```bash
# Test available payment methods
curl -X GET http://*************:8080/api/v1/payments/methods/zimbabwe \
  -H "Authorization: Bearer YOUR_TOKEN"

# Expected response:
{
  "success": true,
  "data": {
    "ecocash": {
      "enabled": true,
      "configured": true,
      "name": "EcoCash"
    },
    "zipit": {
      "enabled": true,
      "configured": true,
      "name": "Zipit"
    },
    "card": {
      "enabled": true,
      "configured": true,
      "name": "Credit/Debit Card"
    }
  }
}
```

### 2. Test EcoCash Payment
```bash
curl -X POST http://*************:8080/api/v1/payments/ecocash \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "booking_id": "booking-uuid",
    "amount": 25.00,
    "phone_number": "0771234567"
  }'
```

### 3. Test Payment Status
```bash
curl -X GET http://*************:8080/api/v1/payments/{transaction_id}/status \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## 🚦 Step 7: Production Deployment

### 1. Security Checklist
- [ ] All API keys secured in environment variables
- [ ] Webhook endpoints use HTTPS
- [ ] Webhook signatures verified
- [ ] Payment amounts validated server-side
- [ ] Transaction logs secured
- [ ] PCI compliance reviewed (for cards)

### 2. Monitoring Setup
- [ ] Payment success/failure rates tracked
- [ ] Transaction processing times monitored
- [ ] Failed payment alerts configured
- [ ] Reconciliation processes automated
- [ ] Fraud detection enabled

### 3. Customer Support
- [ ] Payment support contact information
- [ ] Refund process documented
- [ ] Dispute resolution procedures
- [ ] Payment method help guides
- [ ] Transaction history access

## 📱 Step 8: Mobile App Payment Flow

### 1. Payment Selection
The mobile app presents available methods:
- **EcoCash**: Most popular in Zimbabwe
- **Zipit**: Bank account payments  
- **Cards**: International cards

### 2. Payment Processing
1. User selects payment method
2. App calls backend payment API
3. Backend initiates payment with gateway
4. User completes payment on their device
5. Webhook confirms payment status
6. App receives confirmation

### 3. Payment Status
- **Pending**: Payment initiated
- **Processing**: Gateway processing
- **Completed**: Payment successful
- **Failed**: Payment unsuccessful
- **Cancelled**: User cancelled payment

## 💡 Step 9: Best Practices

### 1. User Experience
- **Clear Pricing**: Show all fees upfront
- **Quick Processing**: Minimize payment steps
- **Status Updates**: Real-time payment status
- **Error Handling**: Clear error messages
- **Payment History**: Easy transaction access

### 2. Business Operations
- **Automated Reconciliation**: Match payments to bookings
- **Fraud Prevention**: Monitor suspicious transactions
- **Customer Support**: Quick payment issue resolution
- **Reporting**: Regular payment analytics
- **Compliance**: Meet regulatory requirements

## ✅ Payment Gateway Checklist

### EcoCash Setup
- [ ] Merchant account approved
- [ ] API credentials received
- [ ] Integration tested
- [ ] Webhook configured
- [ ] Live payments working

### Zipit Setup  
- [ ] ZimSwitch account approved
- [ ] API credentials configured
- [ ] Test transactions successful
- [ ] Production environment ready
- [ ] Bank partnerships confirmed

### Card Processing
- [ ] Payment processor selected
- [ ] Merchant account approved
- [ ] API integration complete
- [ ] Security compliance verified
- [ ] Live card payments working

### Banking Integration
- [ ] Bank partnerships established
- [ ] API access granted
- [ ] Account verification working
- [ ] Settlement processes automated

## 🎯 Next Steps

Once payment gateways are configured:
1. ✅ Payment gateway credentials configured
2. 🔄 Deploy and test everything
3. 🔄 Launch to app stores

---

💳 **Payment gateways are now ready for TaskRabitInc!**

## 🔗 Quick Links

- **EcoCash Merchant Portal**: https://merchant.ecocash.co.zw/
- **ZimSwitch Documentation**: https://docs.zimswitch.co.zw/
- **Steward Bank API**: https://developer.stewardbank.co.zw/
- **CBZ Connect**: https://developer.cbz.co.zw/