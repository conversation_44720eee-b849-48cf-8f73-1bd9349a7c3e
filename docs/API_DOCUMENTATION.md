# TaskRabitInc API Documentation

## Base URL
- Development: `http://192.168.100.101:8080/api/v1`
- Production: TBD

## Authentication
All authenticated endpoints require Bearer token in Authorization header:
```
Authorization: Bearer {token}
```

## Endpoints

### Health Check
- **GET** `/health` - Check API health status

### Authentication

#### Login
- **POST** `/auth/login`
```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

#### Register
- **POST** `/auth/register`
```json
{
  "first_name": "<PERSON>",
  "last_name": "<PERSON><PERSON>",
  "email": "<EMAIL>",
  "phone": "+263771234567",
  "password": "password123",
  "password_confirmation": "password123",
  "role": "client|agent",
  "national_id": "63-123456X63" // Required for agents
}
```

#### Logout
- **POST** `/auth/logout` (Authenticated)

#### Forgot Password
- **POST** `/auth/forgot-password`
```json
{
  "email": "<EMAIL>"
}
```

### User Profile

#### Get Current User
- **GET** `/user` (Authenticated)

#### Update Profile
- **PUT** `/users/profile` (Authenticated)
```json
{
  "first_name": "John",
  "last_name": "Doe",
  "phone": "+263771234567"
}
```

#### Upload Profile Photo
- **POST** `/runner/profile/photo` (Authenticated)
- Multipart form data with `photo` field

### Services

#### List Services
- **GET** `/services` (Authenticated)
- Query params: `category`, `search`, `page`, `per_page`

#### Get Service Details
- **GET** `/services/{id}` (Authenticated)

### Bookings (Customer)

#### List My Bookings
- **GET** `/bookings` (Authenticated)
- Query params: `status`, `page`, `per_page`

#### Create Booking
- **POST** `/bookings` (Authenticated)
```json
{
  "service_id": "uuid",
  "scheduled_date": "2025-09-10",
  "scheduled_time": "14:00:00",
  "service_location": "123 Main St, City",
  "location_coordinates": {
    "latitude": -17.8136,
    "longitude": 31.0522
  },
  "special_instructions": "Please call before arrival"
}
```

#### Get Booking Details
- **GET** `/bookings/{id}` (Authenticated)

#### Cancel Booking
- **POST** `/bookings/{id}/cancel` (Authenticated)
```json
{
  "reason": "Change of plans"
}
```

### Errands (Runner)

#### List Available Errands
- **GET** `/errands/available` (Authenticated - Runner only)

#### Accept Errand
- **POST** `/errands/{id}/accept` (Authenticated - Runner only)

#### List My Errands
- **GET** `/errands/my` (Authenticated - Runner only)
- Query params: `status`, `page`, `per_page`

#### Update Errand Status
- **PATCH** `/errands/{id}/status` (Authenticated - Runner only)
```json
{
  "status": "in_progress|completed"
}
```

### Runner Management

#### Get Earnings History
- **GET** `/runners/earnings/history` (Authenticated - Runner only)

#### Request Withdrawal
- **POST** `/runners/earnings/withdraw` (Authenticated - Runner only)
```json
{
  "amount": 100.00,
  "payment_method": "ecocash|bank_transfer",
  "account_details": {
    "account_number": "**********"
  }
}
```

#### Update Availability
- **POST** `/runners/availability` (Authenticated - Runner only)
```json
{
  "is_available": true
}
```

#### Upload Documents
- **POST** `/runner/documents/upload` (Authenticated - Runner only)
- Multipart form data with document files

#### Get Documents
- **GET** `/runner/documents` (Authenticated - Runner only)

### Notifications

#### List Notifications
- **GET** `/notifications` (Authenticated)
- Query params: `read`, `page`, `per_page`

#### Mark Notification as Read
- **PATCH** `/notifications/{id}/read` (Authenticated)

#### Register Device Token (FCM)
- **POST** `/notifications/device` (Authenticated)
```json
{
  "token": "fcm_device_token",
  "platform": "android|ios"
}
```

### Payments

#### Process Payment
- **POST** `/payments/process` (Authenticated)
```json
{
  "booking_id": "uuid",
  "payment_method": "ecocash|zipit|card",
  "amount": 100.00,
  "phone_number": "**********" // For mobile money
}
```

#### Get Payment Status
- **GET** `/payments/{transaction_id}/status` (Authenticated)

## Response Format

### Success Response
```json
{
  "success": true,
  "message": "Operation successful",
  "data": {}
}
```

### Error Response
```json
{
  "success": false,
  "message": "Error message",
  "errors": {
    "field": ["Error detail"]
  }
}
```

## Status Codes
- 200: Success
- 201: Created
- 400: Bad Request
- 401: Unauthorized
- 403: Forbidden
- 404: Not Found
- 422: Validation Error
- 500: Server Error

## Rate Limiting
- Health endpoint: 30 requests per minute
- Auth endpoints: 5 requests per minute
- Other endpoints: 60 requests per minute