# 🔥 Firebase Configuration Setup Guide

## 📋 Overview
This guide walks through setting up Firebase credentials for the TaskRabitInc mobile app.

## 🚀 Step 1: Create Firebase Project

### 1. Go to Firebase Console
- Visit: https://console.firebase.google.com/
- Sign in with Google account
- Click "Create a project"

### 2. Create Project
- **Project name**: `TaskRabitInc`
- **Project ID**: `taskrabitinc-mobile` (or similar)
- Enable Google Analytics (recommended)
- Choose analytics location: **Zimbabwe** or **United States**

### 3. Enable Required Services
After project creation, enable these services:

#### Cloud Messaging (FCM)
1. Go to **Project Settings** > **Cloud Messaging**
2. **No additional setup needed** - FCM is enabled by default

#### Firestore Database
1. Go to **Firestore Database**
2. Click "Create database"
3. **Start in production mode** (we'll configure rules later)
4. Choose location: **us-central** (closest to Africa)

#### Authentication
1. Go to **Authentication**
2. Click "Get started"
3. Enable **Email/Password** provider
4. Enable **Google** provider (optional, for future use)

## 🔐 Step 2: Configure Android App

### 1. Add Android App
1. In Firebase Console, click "Add app" > Android
2. **Android package name**: `com.taskrabitinc.taskrabbit`
3. **App nickname**: `TaskRabitInc Android`
4. **SHA-1 certificate**: Leave blank for now (add later for production)

### 2. Download Configuration
1. Download `google-services.json`
2. Place in: `android/app/google-services.json`

### 3. Verify Android Configuration
Check these files exist:
```
android/app/google-services.json  ✓
android/build.gradle              ✓ (should have google-services plugin)
android/app/build.gradle          ✓ (should apply google-services plugin)
```

## 📱 Step 3: Configure iOS App

### 1. Add iOS App
1. In Firebase Console, click "Add app" > iOS
2. **iOS bundle ID**: `com.taskrabitinc.taskrabbit`
3. **App nickname**: `TaskRabitInc iOS`
4. **App Store ID**: Leave blank for now

### 2. Download Configuration
1. Download `GoogleService-Info.plist`
2. Place in: `ios/Runner/GoogleService-Info.plist`
3. **Important**: Add to Xcode project (drag into Runner folder)

### 3. Verify iOS Configuration
Check these files exist:
```
ios/Runner/GoogleService-Info.plist  ✓
ios/Podfile                          ✓ (should have Firebase pods)
```

## 🔑 Step 4: Get Server Credentials

### 1. Service Account Key (for Backend)
1. Go to **Project Settings** > **Service accounts**
2. Click "Generate new private key"
3. Download JSON file
4. **Secure storage**: Save as `firebase-service-account.json`
5. **Update backend** `.env` file:
   ```env
   FIREBASE_CREDENTIALS_PATH="/path/to/firebase-service-account.json"
   FIREBASE_PROJECT_ID="your-project-id"
   ```

### 2. FCM Server Key (for Backend)
1. Go to **Project Settings** > **Cloud Messaging**
2. Copy **Server key**
3. **Update backend** `.env` file:
   ```env
   FCM_SERVER_KEY="your-fcm-server-key"
   ```

## 🛡️ Step 5: Configure Firestore Security Rules

### 1. Firestore Rules
Replace default rules with:
```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Runners presence - only authenticated agents can write their own data
    match /runners_presence/{userId} {
      allow read: if request.auth != null;
      allow write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Clients presence - only authenticated clients can write their own data
    match /clients_presence/{userId} {
      allow read: if request.auth != null;
      allow write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Deny all other access
    match /{document=**} {
      allow read, write: if false;
    }
  }
}
```

### 2. Apply Rules
1. Go to **Firestore Database** > **Rules**
2. Replace with above rules
3. Click **Publish**

## 🧪 Step 6: Test Firebase Integration

### 1. Test with Current Server
```bash
# Update server URL in test
BASE_URL="http://*************:8080/api/v1"

# Test Firebase health (if endpoint exists)
curl -X GET "$BASE_URL/health" | grep firebase

# Test FCM token registration (once implemented)
curl -X POST "$BASE_URL/notifications/device" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "token": "test_fcm_token",
    "platform": "android",
    "app_version": "1.0.0"
  }'
```

### 2. Test Mobile App
Once Firebase is configured:
```bash
# Run Flutter app and check console for:
# ✅ Firebase initialized
# ✅ FCM token generated  
# ✅ Notification permissions granted
# ✅ Firestore connection successful
```

## 📝 Step 7: Production Configuration

### 1. Production Security
- **Firestore Rules**: Review and tighten security rules
- **API Keys**: Restrict API key usage to your domain/app
- **Authentication**: Configure proper OAuth settings

### 2. Performance
- **Firestore**: Set up indexes for better query performance
- **FCM**: Monitor message delivery rates
- **Analytics**: Review app usage patterns

## ✅ Configuration Checklist

### Firebase Console Setup
- [ ] Project created with correct name
- [ ] Cloud Messaging enabled
- [ ] Firestore database created
- [ ] Authentication providers enabled
- [ ] Android app added with package name
- [ ] iOS app added with bundle ID

### Mobile App Configuration  
- [ ] `google-services.json` in correct location
- [ ] `GoogleService-Info.plist` in correct location
- [ ] Android build.gradle configured
- [ ] iOS Podfile configured
- [ ] Firebase SDK versions compatible

### Backend Configuration
- [ ] Service account key downloaded securely
- [ ] FCM server key copied
- [ ] Environment variables updated
- [ ] Firebase credentials path correct

### Security & Rules
- [ ] Firestore security rules configured
- [ ] API key restrictions applied
- [ ] Authentication settings reviewed

### Testing
- [ ] Firebase initialization works in mobile app
- [ ] FCM tokens generate successfully
- [ ] Backend can send notifications
- [ ] Firestore read/write works
- [ ] Authentication flow works

## 🚨 Important Notes

### Security
- **Never commit** Firebase config files to public repositories
- **Restrict API keys** to prevent unauthorized usage
- **Use environment variables** for sensitive backend credentials

### Testing vs Production
- **Use separate projects** for testing and production
- **Different package names** for debug vs release builds
- **Monitor usage** and adjust quotas as needed

## 🎯 Next Steps

Once Firebase is configured:
1. ✅ Firebase credentials configured
2. 🔄 Set up payment gateway credentials
3. 🔄 Deploy and test everything
4. 🔄 Launch to app stores

---

🔥 **Firebase is now ready for TaskRabitInc mobile app!**