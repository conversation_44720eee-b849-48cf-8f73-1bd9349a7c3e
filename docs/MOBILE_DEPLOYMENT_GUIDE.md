# 🚀 TaskRabit Mobile App Backend Deployment Guide

## 📋 Overview

This guide provides step-by-step instructions to deploy the TaskRabit backend with full mobile app support.

## ✅ Pre-Deployment Checklist

### 1. Environment Configuration
Copy the mobile environment variables to your `.env` file:

```env
# Firebase Configuration for Mobile App
FIREBASE_CREDENTIALS_PATH="/path/to/service-account-key.json"
FIREBASE_PROJECT_ID="taskrabbit-app"
FCM_SERVER_KEY="your_fcm_server_key"

# Payment Gateway Configuration
ECOCASH_API_URL="https://ecocash-api.econet.co.zw"
ECOCASH_MERCHANT_CODE="your_merchant_code"
ECOCASH_API_KEY="your_api_key"

ZIPIT_API_URL="https://api.zipit.co.zw"
ZIPIT_MERCHANT_ID="your_merchant_id"
ZIPIT_API_SECRET="your_api_secret"

# App Configuration
APP_MOBILE_VERSION="1.0.0"
LOCATION_TRACKING_ENABLED=true
NOTIFICATIONS_ENABLED=true
```

### 2. Database Migration
Run the new database migrations:

```bash
php artisan migrate
```

### 3. Seed Test Data (Optional)
For testing purposes, run the mobile app seeder:

```bash
php artisan db:seed --class=MobileAppTestSeeder
```

## 🔧 System Requirements

- PHP 8.1+
- Laravel 10+
- MySQL 8.0+
- Firebase Cloud Messaging account
- EcoCash/Zipit merchant accounts (for production)

## 📱 Mobile API Endpoints

### Authentication
- `POST /api/v1/auth/mobile/login`
- `POST /api/v1/auth/mobile/register`
- `POST /api/v1/auth/mobile/refresh-token`

### Notifications
- `POST /api/v1/notifications/device` - Register FCM token
- `DELETE /api/v1/notifications/device` - Unregister device
- `POST /api/v1/notifications/send` - Send notification (admin)
- `POST /api/v1/notifications/runners/nearby` - Notify nearby runners (admin)

### Location Tracking
- `POST /api/v1/errands/{id}/update-location` - Update runner location
- `GET /api/v1/errands/{id}/tracking` - Get tracking data

### Ratings
- `POST /api/v1/ratings` - Submit rating
- `GET /api/v1/users/{id}/ratings` - Get user ratings
- `GET /api/v1/users/{id}/rating-summary` - Get rating summary

### Payments (Zimbabwe)
- `GET /api/v1/payments/methods/zimbabwe` - Get payment methods
- `POST /api/v1/payments/ecocash` - Process EcoCash payment
- `POST /api/v1/payments/zipit` - Process Zipit payment
- `GET /api/v1/payments/{id}/status` - Get payment status

### Health & Monitoring
- `GET /api/v1/health` - Basic health check
- `GET /api/v1/health/detailed` - Detailed mobile health check
- `GET /api/v1/health/mobile` - Mobile-specific health check (auth required)
- `GET /api/v1/health/stats` - API statistics (auth required)

### Configuration
- `GET /api/v1/config` - Mobile app configuration

### Testing (Development Only)
- `POST /api/v1/test/notification` - Test FCM notification
- `POST /api/v1/test/location-update` - Test location update
- `POST /api/v1/test/payment` - Test payment processing
- `GET /api/v1/test/status` - System status

## 🔐 Firebase Setup

### 1. Create Firebase Project
1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Create new project: "TaskRabit Mobile"
3. Enable Cloud Messaging

### 2. Generate Service Account Key
1. Go to Project Settings > Service Accounts
2. Generate new private key
3. Download JSON file
4. Place in secure location and update `FIREBASE_CREDENTIALS_PATH`

### 3. Get Server Key
1. Go to Project Settings > Cloud Messaging
2. Copy Server Key
3. Add to `FCM_SERVER_KEY` environment variable

## 💳 Payment Gateway Setup

### EcoCash Configuration
1. Contact Econet Zimbabwe for merchant account
2. Get API credentials:
   - API URL
   - Merchant Code
   - API Key
3. Update environment variables

### Zipit Configuration
1. Contact Zipit for merchant account
2. Get API credentials:
   - API URL
   - Merchant ID
   - API Secret
3. Update environment variables

## 🧪 Testing the Integration

### 1. Health Check
```bash
curl http://your-domain.com/api/v1/health
```

Expected response:
```json
{
  "status": "healthy",
  "timestamp": "2025-09-08T12:00:00Z",
  "version": "1.0.0"
}
```

### 2. Mobile Authentication
```bash
curl -X POST http://your-domain.com/api/v1/auth/mobile/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123",
    "device_name": "test_device"
  }'
```

### 3. FCM Token Registration
```bash
curl -X POST http://your-domain.com/api/v1/notifications/device \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "token": "fcm_device_token_here",
    "platform": "android",
    "app_version": "1.0.0"
  }'
```

### 4. Test Data Verification
If you ran the seeder, test with these accounts:
- **Client**: <EMAIL> / password123
- **Runner**: <EMAIL> / password123
- **Admin**: <EMAIL> / password123

## 🚦 Production Deployment

### 1. Security Checklist
- [ ] Update all default passwords
- [ ] Configure proper HTTPS
- [ ] Set up rate limiting
- [ ] Configure CORS properly
- [ ] Disable debug mode (`APP_DEBUG=false`)
- [ ] Set proper session/cookie security

### 2. Performance Optimization
- [ ] Configure Redis for caching
- [ ] Set up queue workers for notifications
- [ ] Optimize database indexes
- [ ] Configure CDN for file uploads

### 3. Monitoring Setup
- [ ] Set up error logging (Sentry, Bugsnag)
- [ ] Configure server monitoring
- [ ] Set up database monitoring
- [ ] Monitor FCM delivery rates

### 4. Backup Strategy
- [ ] Automated database backups
- [ ] File storage backups
- [ ] Environment configuration backups

## 📊 Key Metrics to Monitor

### API Performance
- Response times for mobile endpoints
- Error rates by endpoint
- Authentication success rates
- FCM delivery rates

### Mobile App Usage
- Daily/Monthly active users
- Errand completion rates
- Payment success rates
- User rating averages

### System Health
- Database performance
- Server resource usage
- Third-party service availability
- Queue processing times

## 🆘 Troubleshooting

### Common Issues

#### FCM Notifications Not Working
1. Check `FCM_SERVER_KEY` configuration
2. Verify Firebase project settings
3. Check device token registration
4. Review notification logs: `GET /api/v1/health/stats`

#### Authentication Failures
1. Verify Sanctum configuration
2. Check token expiration settings
3. Review middleware configuration
4. Check user status (active/suspended)

#### Payment Processing Issues
1. Verify payment gateway credentials
2. Check network connectivity to payment APIs
3. Review payment method configuration
4. Check transaction logs

#### Location Tracking Problems
1. Verify errand status (must be in_progress)
2. Check runner assignment
3. Review location update permissions
4. Check database tracking records

## 📞 Support

For deployment issues or questions:
- Check logs: `storage/logs/laravel.log`
- Run health check: `GET /api/v1/health/detailed`
- Review test endpoints for debugging
- Check database migrations status

## 🔄 Version Updates

When updating the mobile app backend:

1. **Backup database**
2. **Run new migrations**
   ```bash
   php artisan migrate
   ```
3. **Clear caches**
   ```bash
   php artisan config:clear
   php artisan route:clear
   php artisan view:clear
   ```
4. **Update mobile app configuration**
   ```bash
   curl http://your-domain.com/api/v1/config
   ```
5. **Test critical endpoints**
6. **Update mobile app if needed**

---

🎉 **Congratulations!** Your TaskRabit mobile app backend is now ready for production deployment.