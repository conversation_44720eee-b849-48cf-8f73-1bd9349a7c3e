# 🚀 Backend Integration Guide for TaskRabitInc Mobile App

## 📋 Overview

This guide provides the backend development team with all necessary requirements to ensure the Flutter mobile app works seamlessly with the Laravel API. The mobile app is fully developed and tested - this guide covers what backend changes/additions are needed.

---

## 🎯 Critical Requirements Summary

### ✅ **Working Currently:**
- Health endpoint: `GET /api/v1/health`
- Standard authentication: `POST /api/v1/auth/login`
- Services: `GET /api/v1/services`
- Bookings: `GET /api/v1/bookings`
- User errands: `GET /api/v1/errands/user`
- Available errands: `GET /api/v1/errands/available`
- Notifications: `GET /api/v1/notifications`

### 🔧 **Needs Implementation/Updates:**
1. Mobile-specific authentication endpoints
2. FCM token registration endpoint
3. Real-time location tracking endpoints
4. Runner profile management endpoints
5. Rating system endpoints
6. Enhanced payment endpoints
7. Notification sending capabilities

---

## 🔐 1. Mobile Authentication Endpoints

### Required Endpoints:

#### Mobile Login
```php
POST /api/v1/auth/mobile/login
```
**Request Body:**
```json
{
    "email": "<EMAIL>",
    "password": "password123",
    "device_name": "mobile_app"
}
```
**Response:**
```json
{
    "success": true,
    "message": "Login successful",
    "data": {
        "user": {
            "id": "uuid",
            "first_name": "John",
            "last_name": "Doe",
            "full_name": "John Doe",
            "email": "<EMAIL>",
            "phone": "+************",
            "role": "client|agent",
            "status": "active",
            "email_verified": true,
            "phone_verified": true,
            "fully_verified": true,
            "created_at": "2025-01-01T00:00:00Z",
            "agent_profile": { /* if role is agent */ }
        },
        "token": "sanctum_token",
        "token_type": "Bearer",
        "requires_verification": false
    }
}
```

#### Mobile Registration
```php
POST /api/v1/auth/mobile/register
```
**Request Body:**
```json
{
    "first_name": "John",
    "last_name": "Doe", 
    "email": "<EMAIL>",
    "phone": "+************",
    "password": "password123",
    "password_confirmation": "password123",
    "role": "client|agent",
    "device_name": "mobile_app",
    "national_id": "63-123456X63",  // Required for agents
    "address": "123 Main St"        // Optional
}
```

#### Token Refresh
```php
POST /api/v1/auth/mobile/refresh-token
```
**Headers:** `Authorization: Bearer {token}`
**Response:** Same as login response with new token

---

## 📱 2. FCM Token Management

### Device Token Registration
```php
POST /api/v1/notifications/device
```
**Headers:** `Authorization: Bearer {token}`
**Request Body:**
```json
{
    "token": "fcm_device_token_string",
    "platform": "android|ios",
    "app_version": "1.0.0"
}
```
**Response:**
```json
{
    "success": true,
    "message": "Device token registered successfully"
}
```

### Implementation Notes:
- Store FCM tokens in `user_devices` table
- Link tokens to authenticated user
- Handle token refresh (tokens can change)
- Clean up old/invalid tokens

### Suggested Database Schema:
```sql
CREATE TABLE user_devices (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    user_id CHAR(36) NOT NULL,
    fcm_token VARCHAR(512) NOT NULL,
    platform ENUM('android', 'ios') NOT NULL,
    app_version VARCHAR(50),
    is_active BOOLEAN DEFAULT true,
    last_used_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_user_id (user_id),
    INDEX idx_fcm_token (fcm_token),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);
```

---

## 🗺️ 3. Location Tracking Endpoints

### Update Runner Location
```php
POST /api/v1/errands/{id}/update-location
```
**Headers:** `Authorization: Bearer {token}` (Runner only)
**Request Body:**
```json
{
    "latitude": -17.8136,
    "longitude": 31.0522,
    "timestamp": "2025-09-08T12:00:00Z"
}
```
**Response:**
```json
{
    "success": true,
    "message": "Location updated successfully"
}
```

### Get Errand Tracking Data
```php
GET /api/v1/errands/{id}/tracking
```
**Headers:** `Authorization: Bearer {token}` (Client only)
**Response:**
```json
{
    "success": true,
    "data": {
        "errand_id": "uuid",
        "runner_location": {
            "latitude": -17.8136,
            "longitude": 31.0522,
            "updated_at": "2025-09-08T12:00:00Z"
        },
        "estimated_arrival": "2025-09-08T12:30:00Z",
        "status": "in_progress"
    }
}
```

### Implementation Notes:
- Store location data in `errand_tracking` table
- Only allow location updates for assigned runner
- Only allow tracking access for errand client
- Consider implementing location history for completed errands

---

## 👨‍💼 4. Runner Profile Management

### Get Runner Profile
```php
GET /api/v1/runners/profile
```
**Headers:** `Authorization: Bearer {token}` (Agent only)
**Response:**
```json
{
    "success": true,
    "data": {
        "id": "uuid",
        "user": {
            "id": "uuid",
            "first_name": "John",
            "last_name": "Doe",
            "email": "<EMAIL>",
            "phone": "+************"
        },
        "verification_status": "pending|verified|rejected",
        "rating_average": 4.5,
        "total_jobs_completed": 25,
        "is_available": true,
        "documents": [
            {
                "type": "national_id",
                "status": "verified",
                "uploaded_at": "2025-01-01T00:00:00Z"
            }
        ]
    }
}
```

### Update Runner Profile
```php
PUT /api/v1/runners/profile
```
**Headers:** `Authorization: Bearer {token}` (Agent only)
**Request Body:**
```json
{
    "first_name": "John",
    "last_name": "Doe",
    "phone": "+************",
    "address": "123 Main St",
    "is_available": true
}
```

### Get Verification Status
```php
GET /api/v1/runner/verification/status
```
**Headers:** `Authorization: Bearer {token}` (Agent only)
**Response:**
```json
{
    "success": true,
    "data": {
        "overall_status": "pending|verified|rejected",
        "documents": {
            "national_id": "verified",
            "drivers_license": "pending",
            "bank_details": "rejected"
        },
        "next_steps": ["Upload clear photo of driver's license"]
    }
}
```

### Get Performance Overview
```php
GET /api/v1/runner/performance/overview
```
**Headers:** `Authorization: Bearer {token}` (Agent only)
**Response:**
```json
{
    "success": true,
    "data": {
        "total_earnings": 1250.00,
        "jobs_completed": 45,
        "average_rating": 4.7,
        "completion_rate": 95.5,
        "current_month": {
            "earnings": 380.00,
            "jobs": 12
        },
        "top_categories": ["delivery", "shopping"]
    }
}
```

---

## ⭐ 5. Rating System Endpoints

### Submit Rating
```php
POST /api/v1/ratings
```
**Headers:** `Authorization: Bearer {token}`
**Request Body:**
```json
{
    "errand_id": "uuid",
    "rated_user_id": "uuid",
    "rating": 5,
    "comment": "Excellent service!"
}
```
**Response:**
```json
{
    "success": true,
    "message": "Rating submitted successfully",
    "data": {
        "id": "rating_uuid",
        "rating": 5,
        "comment": "Excellent service!"
    }
}
```

### Get User Ratings
```php
GET /api/v1/users/{id}/ratings
```
**Response:**
```json
{
    "success": true,
    "data": [
        {
            "id": "uuid",
            "errand_id": "uuid",
            "rater_id": "uuid",
            "rated_user_id": "uuid", 
            "rating": 5,
            "comment": "Great work!",
            "created_at": "2025-01-01T00:00:00Z",
            "rater": {
                "name": "John Doe",
                "profile_picture": "url"
            }
        }
    ]
}
```

### Get Rating Summary
```php
GET /api/v1/users/{id}/rating-summary
```
**Response:**
```json
{
    "success": true,
    "data": {
        "average_rating": 4.7,
        "total_ratings": 23,
        "rating_distribution": {
            "5": 15,
            "4": 6,
            "3": 2,
            "2": 0,
            "1": 0
        }
    }
}
```

---

## 💳 6. Enhanced Payment Endpoints

### Zimbabwe Payment Methods
```php
GET /api/v1/payments/methods/zimbabwe
```
**Headers:** `Authorization: Bearer {token}`
**Response:**
```json
{
    "success": true,
    "data": {
        "ecocash": {
            "enabled": true,
            "configured": true,
            "name": "EcoCash",
            "icon": "ecocash_logo_url"
        },
        "zipit": {
            "enabled": true,
            "configured": true,
            "name": "Zipit",
            "icon": "zipit_logo_url"
        },
        "card": {
            "enabled": true,
            "configured": true,
            "name": "Credit/Debit Card",
            "icon": "card_icon_url"
        }
    }
}
```

### Process EcoCash Payment
```php
POST /api/v1/payments/ecocash
```
**Headers:** `Authorization: Bearer {token}`
**Request Body:**
```json
{
    "booking_id": "uuid",
    "amount": 25.00,
    "phone_number": "0771234567"
}
```
**Response:**
```json
{
    "success": true,
    "message": "Payment initiated",
    "data": {
        "transaction_id": "uuid",
        "status": "pending",
        "reference": "ECO123456789"
    }
}
```

### Get Payment Status
```php
GET /api/v1/payments/{transaction_id}/status
```
**Headers:** `Authorization: Bearer {token}`
**Response:**
```json
{
    "success": true,
    "data": {
        "transaction_id": "uuid",
        "status": "completed|pending|failed",
        "amount": 25.00,
        "reference": "ECO123456789"
    }
}
```

---

## 📢 7. Notification System

### Send Notification to User
```php
POST /api/v1/notifications/send
```
**Headers:** `Authorization: Bearer {token}` (Admin or system)
**Request Body:**
```json
{
    "user_id": "uuid",
    "title": "Errand Update",
    "body": "Your errand has been accepted",
    "data": {
        "type": "errand_update",
        "errand_id": "uuid",
        "screen": "/errands/details"
    }
}
```

### Notify Runners in Area
```php
POST /api/v1/notifications/runners/nearby
```
**Headers:** `Authorization: Bearer {token}` (System)
**Request Body:**
```json
{
    "latitude": -17.8136,
    "longitude": 31.0522,
    "radius_km": 5.0,
    "title": "New Errand Available",
    "body": "Delivery errand - $25.00",
    "data": {
        "type": "new_errand",
        "errand_id": "uuid"
    }
}
```

### Implementation Requirements:
- Integrate with Firebase Cloud Messaging (FCM)
- Store FCM server key in environment variables
- Queue notifications for better performance
- Handle failed notification delivery
- Log notification delivery status

---

## 🏗️ 8. Database Schema Updates

### Errand Tracking Table
```sql
CREATE TABLE errand_tracking (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    errand_id CHAR(36) NOT NULL,
    runner_id CHAR(36) NOT NULL,
    latitude DECIMAL(10, 8) NOT NULL,
    longitude DECIMAL(11, 8) NOT NULL,
    accuracy DECIMAL(8, 2),
    recorded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_errand_id (errand_id),
    INDEX idx_runner_id (runner_id),
    INDEX idx_recorded_at (recorded_at),
    FOREIGN KEY (errand_id) REFERENCES errands(id) ON DELETE CASCADE,
    FOREIGN KEY (runner_id) REFERENCES users(id) ON DELETE CASCADE
);
```

### Ratings Table
```sql
CREATE TABLE ratings (
    id CHAR(36) PRIMARY KEY,
    errand_id CHAR(36) NOT NULL,
    rater_id CHAR(36) NOT NULL,
    rated_user_id CHAR(36) NOT NULL,
    rating TINYINT UNSIGNED NOT NULL CHECK (rating >= 1 AND rating <= 5),
    comment TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_errand_rater (errand_id, rater_id),
    INDEX idx_rated_user (rated_user_id),
    INDEX idx_rating (rating),
    FOREIGN KEY (errand_id) REFERENCES errands(id) ON DELETE CASCADE,
    FOREIGN KEY (rater_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (rated_user_id) REFERENCES users(id) ON DELETE CASCADE
);
```

### Notification Logs Table
```sql
CREATE TABLE notification_logs (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    user_id CHAR(36) NOT NULL,
    title VARCHAR(255) NOT NULL,
    body TEXT NOT NULL,
    data JSON,
    fcm_response JSON,
    status ENUM('sent', 'failed', 'delivered') DEFAULT 'sent',
    sent_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_user_id (user_id),
    INDEX idx_status (status),
    INDEX idx_sent_at (sent_at),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);
```

---

## 🔧 9. Environment Configuration

### Required Environment Variables
```env
# Firebase Configuration
FIREBASE_CREDENTIALS_PATH="/path/to/service-account-key.json"
FIREBASE_PROJECT_ID="taskrabbit-app"
FCM_SERVER_KEY="your_fcm_server_key"

# Payment Configuration
ECOCASH_API_URL="https://ecocash-api.econet.co.zw"
ECOCASH_MERCHANT_CODE="your_merchant_code"
ECOCASH_API_KEY="your_api_key"

ZIPIT_API_URL="https://api.zipit.co.zw"
ZIPIT_MERCHANT_ID="your_merchant_id"
ZIPIT_API_SECRET="your_api_secret"

# App Configuration
APP_MOBILE_VERSION="1.0.0"
LOCATION_TRACKING_ENABLED=true
NOTIFICATIONS_ENABLED=true
```

---

## 🚨 10. Security Requirements

### API Security
- **Rate Limiting**: Implement rate limiting for all endpoints
- **Input Validation**: Validate all request inputs
- **SQL Injection Protection**: Use parameterized queries
- **CORS Configuration**: Proper CORS setup for mobile clients
- **Token Validation**: Verify Sanctum tokens on all protected routes

### Location Data Security
- **Encryption**: Encrypt sensitive location data
- **Access Control**: Only allow authorized access to tracking data
- **Data Retention**: Implement location data retention policies
- **Privacy Compliance**: Ensure compliance with data protection laws

### Notification Security
- **FCM Token Validation**: Validate FCM tokens before storing
- **Message Content**: Sanitize notification content
- **User Consent**: Respect user notification preferences
- **Token Cleanup**: Regular cleanup of invalid tokens

---

## 🧪 11. Testing Requirements

### Required Test Endpoints
Create these endpoints for testing mobile app integration:

```php
// Test notification sending
POST /api/v1/notifications/test
{
    "title": "Test Notification",
    "body": "Testing FCM integration"
}

// Test location update
POST /api/v1/test/location-update
{
    "errand_id": "uuid",
    "latitude": -17.8136,
    "longitude": 31.0522
}

// Test payment simulation
POST /api/v1/test/payment
{
    "amount": 25.00,
    "method": "ecocash"
}
```

### Testing Checklist
- [ ] Mobile authentication endpoints work
- [ ] FCM token registration successful
- [ ] Location tracking updates correctly
- [ ] Notifications send and receive properly
- [ ] Payment processing works with Zimbabwe methods
- [ ] Rating system functions correctly
- [ ] All endpoints return proper error messages
- [ ] Rate limiting works as expected

---

## 📋 12. Deployment Checklist

### Before Deployment:
- [ ] All new endpoints implemented and tested
- [ ] Database migrations run successfully
- [ ] Environment variables configured
- [ ] FCM credentials properly set up
- [ ] Payment gateway integrations tested
- [ ] Security measures implemented
- [ ] Rate limiting configured
- [ ] Error logging enabled
- [ ] Performance monitoring set up

### After Deployment:
- [ ] Health check endpoint returns success
- [ ] Mobile app authentication works
- [ ] Push notifications deliver correctly
- [ ] Location tracking functions properly
- [ ] Payment processing works
- [ ] All endpoints respond within acceptable time limits

---

## 📞 13. Support & Troubleshooting

### Common Issues:

#### FCM Token Issues
- **Problem**: Tokens not registering
- **Solution**: Check FCM credentials and Firebase project configuration

#### Location Updates Not Working
- **Problem**: Location data not updating
- **Solution**: Verify database permissions and endpoint authentication

#### Notifications Not Sending
- **Problem**: Push notifications failing
- **Solution**: Check FCM server key and token validity

#### Payment Failures
- **Problem**: Payment processing errors
- **Solution**: Verify payment gateway credentials and API endpoints

### Monitoring Endpoints:
```php
GET /api/v1/health/detailed - Extended health check
GET /api/v1/stats/notifications - Notification delivery stats
GET /api/v1/stats/payments - Payment processing stats
GET /api/v1/stats/active-users - Real-time user activity
```

---

## 🎯 Priority Implementation Order

### Phase 1 (Critical - App Won't Work Without These):
1. Mobile authentication endpoints
2. FCM token registration endpoint
3. Enhanced user profile responses

### Phase 2 (High Priority - Core Features):
4. Location tracking endpoints
5. Runner profile management
6. Enhanced payment endpoints

### Phase 3 (Medium Priority - Enhanced Features):
7. Rating system endpoints
8. Notification sending capabilities
9. Performance tracking endpoints

### Phase 4 (Low Priority - Optional Features):
10. Advanced analytics endpoints
11. Test endpoints for debugging
12. Enhanced monitoring endpoints

---

## 📧 Contact Information

For questions or clarifications about these requirements, please contact the mobile development team. All endpoints should be implemented following the exact specifications above to ensure seamless mobile app integration.

**Mobile App Status**: ✅ Complete and ready for integration  
**Backend Requirements**: 📋 Detailed in this document  
**Priority**: 🚨 High - Required for app store deployment

---

*Last Updated: September 8, 2025*  
*Current Server: http://192.168.0.188:8080*  
*Mobile App Version: 1.0.0*