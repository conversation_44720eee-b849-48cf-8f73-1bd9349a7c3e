#!/bin/bash

echo "Testing TaskRabitInc API Endpoints"
echo "=================================="

BASE_URL="http://192.168.0.188:8080/api/v1"

# Test Health Endpoint
echo -e "\n1. Testing Health Endpoint..."
curl -s -X GET "$BASE_URL/health" | python3 -c "import sys, json; data = json.load(sys.stdin); print('Status:', data.get('status', 'Failed'))"

# Test Login (try mobile endpoint first, fallback to regular)
echo -e "\n2. Testing Login..."
LOGIN_RESPONSE=$(curl -s -X POST "$BASE_URL/auth/mobile/login" \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password123","device_name":"mobile_app"}')

# If mobile login fails, try regular login
if echo "$LOGIN_RESPONSE" | grep -q "Error\|error"; then
  echo "Mobile login not available, trying regular login..."
  LOGIN_RESPONSE=$(curl -s -X POST "$BASE_URL/auth/login" \
    -H "Content-Type: application/json" \
    -d '{"email":"<EMAIL>","password":"password123"}')
fi

TOKEN=$(echo $LOGIN_RESPONSE | python3 -c "import sys, json; data = json.load(sys.stdin); print(data.get('data', {}).get('token', ''))" 2>/dev/null)

if [ -n "$TOKEN" ]; then
    echo "Login successful! Token received."
    
    # Test Services Endpoint
    echo -e "\n3. Testing Services Endpoint..."
    SERVICES_COUNT=$(curl -s -X GET "$BASE_URL/services" \
      -H "Authorization: Bearer $TOKEN" | python3 -c "import sys, json; data = json.load(sys.stdin); print(len(data.get('data', [])))" 2>/dev/null)
    echo "Found $SERVICES_COUNT services"
    
    # Test Errands Endpoint for Clients
    echo -e "\n4. Testing User Errands Endpoint..."
    curl -s -X GET "$BASE_URL/errands/user" \
      -H "Authorization: Bearer $TOKEN" | python3 -c "import sys, json; data = json.load(sys.stdin); print('User errands endpoint working' if data.get('success') else 'Failed')" 2>/dev/null
    
    # Test Bookings Endpoint
    echo -e "\n5. Testing Bookings Endpoint..."
    BOOKINGS_COUNT=$(curl -s -X GET "$BASE_URL/bookings" \
      -H "Authorization: Bearer $TOKEN" | python3 -c "import sys, json; data = json.load(sys.stdin); print(len(data.get('data', {}).get('data', [])))" 2>/dev/null)
    echo "Found $BOOKINGS_COUNT bookings"
    
    # Test Zimbabwe Payment Methods
    echo -e "\n6. Testing Zimbabwe Payment Methods..."
    curl -s -X GET "$BASE_URL/payments/methods/zimbabwe" \
      -H "Authorization: Bearer $TOKEN" | python3 -c "import sys, json; data = json.load(sys.stdin); print('Zimbabwe payment methods available' if data.get('success') else 'Not available')" 2>/dev/null
    
    # Test Notifications Endpoint
    echo -e "\n7. Testing Notifications Endpoint..."
    curl -s -X GET "$BASE_URL/notifications" \
      -H "Authorization: Bearer $TOKEN" | python3 -c "import sys, json; data = json.load(sys.stdin); print('Notifications endpoint working' if data.get('success') else 'Failed')" 2>/dev/null
    
else
    echo "Login failed!"
fi

echo -e "\n=================================="
echo "API Testing Complete"